// 转换bib.json为JavaScript模块的脚本
const fs = require('fs');
const path = require('path');

function convertJsonToJs() {
    try {
        // 读取bib.json文件
        const jsonPath = path.join(__dirname, 'bib.json');
        const jsonContent = fs.readFileSync(jsonPath, 'utf8');
        const data = JSON.parse(jsonContent);
        
        // 创建JavaScript模块内容
        const jsContent = `// 圣经经文数据
// 此文件由convert-json-to-js.js自动生成，请勿手动编辑

// 圣经经文数据对象
const BibleData = ${JSON.stringify(data, null, 2)};

// 导出数据
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = BibleData;
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.BibleData = BibleData;
}

// 数据统计信息
console.log('📖 圣经数据已加载:', Object.keys(BibleData).length, '条经文');
`;

        // 写入bib-data.js文件
        const jsPath = path.join(__dirname, 'bib-data.js');
        fs.writeFileSync(jsPath, jsContent, 'utf8');
        
        console.log('✅ 转换完成！');
        console.log(`📁 输入文件: ${jsonPath}`);
        console.log(`📁 输出文件: ${jsPath}`);
        console.log(`📊 数据条数: ${Object.keys(data).length}`);
        
        // 计算文件大小
        const jsonSize = fs.statSync(jsonPath).size;
        const jsSize = fs.statSync(jsPath).size;
        console.log(`📏 JSON文件大小: ${(jsonSize / 1024).toFixed(2)} KB`);
        console.log(`📏 JS文件大小: ${(jsSize / 1024).toFixed(2)} KB`);
        
    } catch (error) {
        console.error('❌ 转换失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    convertJsonToJs();
}

module.exports = convertJsonToJs;
