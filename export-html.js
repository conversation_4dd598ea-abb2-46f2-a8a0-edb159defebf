/**
 * 悬停阅读导出HTML功能
 * 这个脚本实现了将悬停阅读内容导出为完整HTML文件的功能，保留所有格式和悬停效果
 */

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const exportHtmlButton = document.getElementById('exportHtmlButton');
    const exportHtmlModal = document.getElementById('exportHtmlModal');
    const exportHtmlClose = document.querySelector('.export-html-close');
    const exportHtmlTextarea = document.getElementById('exportHtmlTextarea');
    const exportHtmlCopyBtn = document.getElementById('exportHtmlCopyBtn');
    const exportHtmlDownloadBtn = document.getElementById('exportHtmlDownloadBtn');
    const hoverContent = document.getElementById('hoverContent');

    // 点击导出HTML按钮显示模态框
    exportHtmlButton.addEventListener('click', function() {
        // 生成完整HTML
        const htmlContent = generateFullHtml();
        // 在文本框中显示
        exportHtmlTextarea.value = htmlContent;
        // 显示模态框
        exportHtmlModal.style.display = 'flex';
    });

    // 关闭模态框
    exportHtmlClose.addEventListener('click', function() {
        exportHtmlModal.style.display = 'none';
    });

    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === exportHtmlModal) {
            exportHtmlModal.style.display = 'none';
        }
    });

    // 复制HTML按钮
    exportHtmlCopyBtn.addEventListener('click', function() {
        exportHtmlTextarea.select();
        document.execCommand('copy');
        
        // 显示复制成功提示
        const originalText = exportHtmlCopyBtn.textContent;
        exportHtmlCopyBtn.textContent = '复制成功!';
        setTimeout(() => {
            exportHtmlCopyBtn.textContent = originalText;
        }, 2000);
    });

    // 下载HTML文件按钮
    exportHtmlDownloadBtn.addEventListener('click', function() {
        const htmlContent = exportHtmlTextarea.value;
        const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '悬停阅读内容.html';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    });

    /**
     * 生成完整的HTML文件内容
     * @returns {string} 完整的HTML文件内容
     */
    function generateFullHtml() {
        // 获取当前页面的CSS样式
        const styles = Array.from(document.styleSheets)
            .filter(sheet => {
                try {
                    return !sheet.href || sheet.href.startsWith(window.location.origin);
                } catch (e) {
                    return false;
                }
            })
            .map(sheet => {
                try {
                    return Array.from(sheet.cssRules)
                        .map(rule => rule.cssText)
                        .join('\n');
                } catch (e) {
                    console.log('无法访问样式表:', e);
                    return '';
                }
            })
            .join('\n');

        // 提取与悬停阅读相关的样式
        const relevantStyles = extractRelevantStyles(styles);

        // 获取悬停阅读内容区域的HTML，并清理空div
        const cleanedHtml = cleanEmptyDivs(hoverContent.innerHTML);

        // 检测当前是否为移动设备，决定是否添加移动设备弹窗代码
        const isMobileDeviceDetection = `
        // 检测是否为移动设备
        const isMobileDevice = window.matchMedia("(hover: none)").matches;
        `;

        // 移动设备弹窗HTML代码（根据设备类型条件渲染）
        const mobileModalHtml = `
    <!-- 移动设备弹窗 - 仅在移动设备上显示 -->
    <div id="mobileVerseModal" class="mobile-verse-modal" style="display: none;">
        <div class="mobile-verse-content">
            <span class="mobile-verse-close">&times;</span>
            <div class="mobile-verse-header" id="mobileVerseHeader"></div>
            <div class="mobile-verse-text" id="mobileVerseText"></div>
        </div>
    </div>`;

        // 移动设备弹窗相关JavaScript代码
        const mobileModalScript = `
            // 检测是否为移动设备（基于是否支持悬停）
            const isMobileDevice = window.matchMedia("(hover: none)").matches;
            
            // 初始化引用元素
            const references = document.querySelectorAll('.reference-hover');
            
            // 使用更可靠的方法禁止背景滚动 - 使用 preventDefault 方法
            let scrollDisabled = false;
            
            // 阻止触摸移动事件
            function preventTouchMove(e) {
                if (scrollDisabled) {
                    e.preventDefault();
                }
            }
            
            // 禁止背景滚动的函数
            function disableBodyScroll() {
                if (!scrollDisabled) {
                    scrollDisabled = true;
                    // 保存当前滚动位置
                    const scrollY = window.scrollY;
                    document.body.style.overflow = 'hidden';
                    document.body.setAttribute('data-scroll-position', scrollY);
                    
                    // 添加事件监听器来阻止触摸移动
                    document.addEventListener('touchmove', preventTouchMove, { passive: false });
                }
            }
            
            // 恢复背景滚动的函数
            function enableBodyScroll() {
                if (scrollDisabled) {
                    scrollDisabled = false;
                    document.body.style.overflow = '';
                    
                    // 移除事件监听器
                    document.removeEventListener('touchmove', preventTouchMove);
                    
                    // 恢复滚动位置
                    const scrollY = parseInt(document.body.getAttribute('data-scroll-position') || '0');
                    window.scrollTo(0, scrollY);
                }
            }
            
            // 关闭弹窗的函数
            function closeModal(modal) {
                if (modal) {
                    modal.style.display = 'none';
                    enableBodyScroll();
                }
            }
            
            // 添加ESC键关闭弹窗
            if (isMobileDevice) {
                const mobileModal = document.getElementById('mobileVerseModal');
                
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && mobileModal.style.display === 'flex') {
                        closeModal(mobileModal);
                    }
                });
            }
            
            // 为每个引用添加事件处理
            references.forEach(ref => {
                // 确保悬停预览初始状态为隐藏
                const preview = ref.querySelector('.verse-preview');
                if (preview) {
                    preview.style.display = 'none';
                }
                
                if (isMobileDevice) {
                    // 移动设备：初始化弹窗元素
                    const mobileModal = document.getElementById('mobileVerseModal');
                    const mobileHeader = document.getElementById('mobileVerseHeader');
                    const mobileText = document.getElementById('mobileVerseText');
                    const mobileClose = document.querySelector('.mobile-verse-close');
                    
                    // 关闭移动设备弹窗
                    mobileClose.addEventListener('click', function() {
                        closeModal(mobileModal);
                    });
                    
                    // 点击弹窗外部关闭
                    mobileModal.addEventListener('click', function(e) {
                        if (e.target === mobileModal) {
                            closeModal(mobileModal);
                        }
                    });
                    
                    // 移动设备：点击显示弹窗
                    ref.addEventListener('click', function(e) {
                        e.preventDefault();
                        
                        // 获取引用内容
                        const previewHeader = preview.querySelector('.verse-preview-header');
                        const previewText = preview.querySelector('.verse-preview-text') || 
                                        preview.querySelector('.verse-preview-not-found');
                        
                        // 填充弹窗内容
                        mobileHeader.textContent = previewHeader ? previewHeader.textContent : '';
                        mobileText.innerHTML = previewText ? previewText.innerHTML : '经文未找到';
                        
                        // 显示弹窗前禁止背景滚动
                        disableBodyScroll();
                        
                        // 显示弹窗
                        mobileModal.style.display = 'flex';
                        
                        // 阻止事件冒泡
                        e.stopPropagation();
                    });
                } else {
                    // 桌面设备：使用鼠标悬停
                    ref.addEventListener('mouseenter', function() {
                        if (preview) {
                            preview.style.display = 'block';
                            
                            // 调整预览框位置，确保不超出窗口
                            const refRect = ref.getBoundingClientRect();
                            const previewRect = preview.getBoundingClientRect();
                            
                            // 检查水平方向
                            if (previewRect.right > window.innerWidth) {
                                preview.style.left = 'auto';
                                preview.style.right = '0';
                            } else {
                                preview.style.left = '0';
                                preview.style.right = 'auto';
                            }
                            
                            // 检查垂直方向
                            if (previewRect.top < 0) {
                                preview.style.bottom = 'auto';
                                preview.style.top = '100%';
                            }
                        }
                    });
                    
                    ref.addEventListener('mouseleave', function() {
                        if (preview) {
                            preview.style.display = 'none';
                        }
                    });
                }
            });`;

        // 构建完整的HTML文档
        return `<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬停阅读导出内容</title>
    <style>
        /* 基本样式 */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333;
        }
        
        .content-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #4CAF50;
        }
        
        /* 标题样式 */
        .title-level-1 {
            font-size: 1.6em;
            font-weight: bold;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
        }
        
        .title-level-2 {
            font-size: 1.4em;
            font-weight: bold;
            margin-top: 1.2em;
            margin-bottom: 0.6em;
            color: #34495e;
            padding-left: 12px;
            border-left: 4px solid #2ecc71;
            position: relative;
            background-color: #f5f9f7;
            padding: 8px 8px 8px 12px;
            border-radius: 0 4px 4px 0;
        }
        
        .title-level-3 {
            font-size: 1.2em;
            font-weight: bold;
            margin-top: 1em;
            margin-bottom: 0.5em;
            color: #2c3e50;
            padding-left: 10px;
            border-left: 3px solid #e74c3c;
            position: relative;
            background-color: #fdf5f5;
            padding: 6px 6px 6px 10px;
            border-radius: 0 3px 3px 0;
        }
        
        .title-level-4 {
            font-size: 1.1em;
            font-weight: bold;
            margin-top: 0.8em;
            margin-bottom: 0.4em;
            color: #7f8c8d;
            padding-left: 8px;
            border-left: 2px solid #9b59b6;
            position: relative;
            background-color: #f9f5fb;
            padding: 5px 5px 5px 8px;
            border-radius: 0 3px 3px 0;
        }
        
        .title-level-5 {
            font-size: 1em;
            font-style: italic;
            font-weight: bold;
            margin-top: 0.6em;
            margin-bottom: 0.3em;
            color: #95a5a6;
            padding-left: 6px;
            border-left: 1px solid #f39c12;
            position: relative;
            background-color: #fef9e7;
            padding: 4px 4px 4px 6px;
            border-radius: 0 2px 2px 0;
        }
        
        .title-level-6 {
            font-size: 0.95em;
            font-weight: bold;
            margin-top: 0.5em;
            margin-bottom: 0.3em;
            color: #16a085;
            text-indent: 1em;
            position: relative;
            background-color: #e8f8f5;
            padding: 3px 3px 3px 5px;
            border-radius: 2px;
        }
        
        /* 正文样式 */
        .body-text {
            margin-bottom: 0.8em;
            line-height: 1.8;
        }
        
        /* 悬停阅读相关样式 */
        ${relevantStyles}
        
        /* 调整悬停预览位置 */
        .reference-hover {
            position: relative;
            display: inline-block;
            cursor: pointer;
            border-bottom: 1px dashed #4CAF50;
            padding: 0 2px;
        }
        
        .verse-preview {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: #fff;
            border: 1px solid #ccc;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            padding: 10px;
            border-radius: 4px;
            width: 300px;
            z-index: 100;
            font-style: italic;
            color: #155724;
            max-height: 200px;
            overflow-y: auto;
        }
        
        /* 确保桌面设备悬停效果能够正常工作 */
        @media (hover: hover) {
            .reference-hover:hover .verse-preview {
                display: block !important;
            }
        }
        
        /* 移动设备弹窗样式 */
        .mobile-verse-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .mobile-verse-content {
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            width: 90%;
            max-width: 350px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        
        .mobile-verse-close {
            position: absolute;
            top: 5px;
            right: 10px;
            font-size: 20px;
            color: #666;
            cursor: pointer;
            z-index: 1;
            padding: 5px;
            line-height: 1;
        }
        
        .mobile-verse-header {
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-right: 20px; /* 为关闭按钮留出空间 */
        }
        
        .mobile-verse-text {
            font-style: italic;
            color: #155724;
            overflow-y: auto;
            max-height: 60vh; /* 确保内容可滚动 */
        }
        
        /* 区分设备的媒体查询 */
        @media (hover: none) {
            /* 移动设备: 禁用悬停效果 */
            .reference-hover:hover .verse-preview {
                display: none !important;
            }
        }

        /* 桌面设备: 隐藏移动设备弹窗 */
        @media (hover: hover) {
            .mobile-verse-modal {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="content-container">
        <h1>悬停阅读内容</h1>
        <div id="content">
            ${cleanedHtml}
        </div>
    </div>
    
    ${mobileModalHtml}
    
    <script>
        // 为桌面和移动设备添加不同的交互支持
        document.addEventListener('DOMContentLoaded', function() {
            ${mobileModalScript}
        });
    </script>
</body>
</html>`;
    }

    /**
     * 从完整CSS中提取与悬停阅读相关的样式
     * @param {string} allStyles 所有样式
     * @returns {string} 与悬停阅读相关的样式
     */
    function extractRelevantStyles(allStyles) {
        const relevantSelectors = [
            '.reference-hover',
            '.verse-preview',
            '.verse-preview-header',
            '.verse-preview-text',
            '.verse-preview-not-found',
            '.verse-num',
            '.scripture-text',
            '.scripture-not-found',
            '.scripture-invalid',
            '.auto-corrected',
            '.correction-note'
        ];
        
        // 使用正则表达式提取相关样式
        const relevantStylesArray = [];
        relevantSelectors.forEach(selector => {
            const regex = new RegExp(`[^}]*${selector}[^{]*{[^}]*}`, 'g');
            const matches = allStyles.match(regex);
            if (matches) {
                relevantStylesArray.push(...matches);
            }
        });
        
        return relevantStylesArray.join('\n');
    }
    
    /**
     * 清理HTML中的空div元素，并为特定格式的div添加标题样式
     * @param {string} html 原始HTML内容
     * @returns {string} 清理后的HTML内容
     */
    function cleanEmptyDivs(html) {
        // 创建临时DOM元素来解析HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // 查找所有div元素
        const allDivs = tempDiv.querySelectorAll('div');
        
        // 遍历所有div元素
        allDivs.forEach(div => {
            // 检查div是否在span内部或是悬停预览相关元素
            const isInsideSpan = div.closest('span') !== null;
            const isSpecialElement = div.classList.contains('verse-preview') || 
                                   div.classList.contains('scripture-text') ||
                                   div.classList.contains('verse-preview-header') ||
                                   div.classList.contains('verse-preview-text') ||
                                   div.classList.contains('verse-preview-not-found') ||
                                   div.classList.contains('scripture-not-found') ||
                                   div.classList.contains('scripture-invalid') ||
                                   div.closest('.verse-preview') !== null;
            
            // 如果是特殊元素，不做处理
            if (isSpecialElement) {
                return;
            }
            
            const text = div.textContent.trim();
            
            // 检查div是否为空（没有内容或只有空白）
            if (text === '') {
                // 移除空div
                div.parentNode.removeChild(div);
                return;
            }
            
            // 如果div在span内部，不应用标题样式
            if (isInsideSpan) {
                return;
            }
            
            // 按顺序检查标题模式
            if (text.match(/^第[一二三四五六七八九十百千万0-9]+篇\s*/)) {
                // 为"第几篇"开头的div添加一级标题样式
                div.className = 'title-level-1';
            }
            else if (text.match(/^[壹贰叁肆伍陆柒捌玖拾佰仟万]+\s*/)) {
                // 为大写数字开头的div添加二级标题样式
                div.className = 'title-level-2';
            }
            else if (text.match(/^[一二三四五六七八九十百千万]+\s*/)) {
                // 为普通数字开头的div添加三级标题样式
                div.className = 'title-level-3';
            }
            else if (text.match(/^[0-9]+([、\.．。])?\s*/)) {
                // 为阿拉伯数字开头的div添加四级标题样式（标点可选）
                div.className = 'title-level-4';
            }
            else if (text.match(/^[a-zA-Z]([、\.．。])?\s*/)) {
                // 为英文字母开头的div添加五级标题样式（标点可选）
                div.className = 'title-level-5';
            }
            else if (text.match(/^[（(][一二三四五六七八九十0-9]+[）)]\s*/)) {
                // 为圆括号中的数字开头的div添加六级标题样式
                div.className = 'title-level-6';
            }
            else {
                // 其他内容设置为正文样式
                div.className = 'body-text';
            }
        });
        
        return tempDiv.innerHTML;
    }
}); 