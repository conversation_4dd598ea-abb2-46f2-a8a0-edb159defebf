<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>圣经引用格式化与经文查询工具</title>
    <style>
        /* 全局样式 */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333;
        }

        /* ... existing styles ... */

        /* 悬停阅读相关样式 */
        .reference-hover {
            border-bottom: 1px dashed #4CAF50;
            position: relative;
            cursor: pointer;
        }

        .verse-preview {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: #fff;
            border: 1px solid #ccc;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: 4px;
            width: 300px;
            z-index: 100;
            font-style: italic;
            color: #155724;
            max-height: 200px;
            overflow-y: auto;
        }

        .verse-preview-header {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
        }

        .verse-num {
            font-weight: bold;
            color: #4CAF50;
        }

        .tab-container {
            margin-top: 20px;
        }

        .tab-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 10px 20px;
            border: none;
            background-color: #f0f0f0;
            cursor: pointer;
            border-radius: 5px;
            font-size: 14px;
        }

        .tab-button.active {
            background-color: #4CAF50;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .file-input-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #eee;
            background-color: #fcfcfc;
            border-radius: 5px;
        }

        #output,
        #originalContent,
        #formatOutput,
        #verseOutput,
        #formatOriginal,
        #verseOriginal,
        #hoverOriginal,
        #hoverContent {
            white-space: pre-wrap;
            word-wrap: break-word;
            border: 1px solid #ccc;
            padding: 15px;

            font-family: "Courier New", Courier, monospace;
            font-size: 0.9em;
            line-height: 1.5;
            width: 100%;
            box-sizing: border-box;
            height: calc(100vh - 350px);
            overflow-y: auto;
            resize: vertical;
        }

        /* 输出区域样式 */
        #output,
        #formatOutput,
        #verseOutput,
        #hoverContent {
            background-color: #f5f5f5;
            cursor: default;
        }

        /* 输入区域样式 */
        #originalContent,
        #formatOriginal,
        #verseOriginal,
        #hoverOriginal {
            background-color: #fff;
            /* 为输入文本区域设置白色背景 */
        }

        .content-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .content-panel {
            flex: 1;
            min-width: 0;
            position: relative;
            /* 添加相对定位用于全屏按钮 */
        }

        .content-panel h2 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }

        .copy-btn,
        .download-btn {
            padding: 8px 16px;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 5px;
        }

        /* 添加全屏按钮样式 */
        .fullscreen-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(76, 175, 80, 0.7);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            font-size: 12px;
            z-index: 10;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .fullscreen-btn:hover {
            background-color: rgba(76, 175, 80, 1);
        }

        .fullscreen-btn svg {
            width: 14px;
            height: 14px;
            fill: currentColor;
        }

        /* 全屏模式样式 */
        .fullscreen-mode {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1000;
            overflow: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        .fullscreen-mode .fullscreen-btn {
            position: fixed;
            top: 20px;
            right: 20px;
        }

        .copy-btn {
            background-color: #4CAF50;
        }

        .download-btn {
            background-color: #2196F3;
        }

        .copy-btn.success {
            background-color: #ff0000;
            /* 复制成功/失败时按钮颜色，可以调整为更合适的颜色 */
        }

        #jsonStatus,
        #hoverJsonStatus,
        #verseJsonStatus {
            font-style: italic;
            color: #666;
            margin-left: 5px;
            font-size: 0.9em;
        }

        /* 加载状态指示器 */
        .loading-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            color: #1976d2;
            font-size: 0.9em;
        }

        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e3f2fd;
            border-top: 2px solid #2196f3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .success-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            color: #2e7d32;
            font-size: 0.9em;
        }

        .error-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background-color: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            color: #c62828;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background-color: #2196f3;
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .scripture-text {
            /* 成功找到的经文的样式 */
            color: #155724;
            background-color: #d4edda;
            border-left: 3px solid #28a745;
            padding: 5px 10px;
            margin-left: 1em;
            margin-top: 2px;
            margin-bottom: 5px;
            display: block;
            font-style: italic;
            font-size: 0.95em;
            border-radius: 3px;
        }

        .scripture-not-found {
            /* 未找到经文的提示样式 */
            color: #721c24;
            background-color: #f8d7da;
            border-left: 3px solid #dc3545;
            padding: 5px 10px;
            margin-left: 1em;
            margin-top: 2px;
            margin-bottom: 5px;
            display: block;
            /* 改为块级元素以确保换行 */
            font-style: italic;
            font-size: 0.95em;
            border-radius: 3px;
        }

        /* --- 新增样式 --- */
        .toggle-original-btn {
            padding: 5px 10px;
            background-color: #6c757d;
            /* 中性灰色 */
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }

        .toggle-original-btn:hover {
            background-color: #5a6268;
        }

        .original-line-wrapper {
            color: #555;
            /* 略微柔和的颜色 */
            font-size: 0.9em;
            margin-bottom: 5px;
            /* 原始行和引用之间的间距 */
            /* border-bottom: 1px dotted #ccc; 分隔线 */
            padding-bottom: 3px;
            margin-top: 10px;
            /* 与上一个分组的间距 */
        }

        .original-line-wrapper.hidden {
            display: none;
        }

        .line-group {
            margin-bottom: 15px;
            /* 每组（原文+结果）之间的间距 */
        }

        .processed-references {
            /* 包含格式化引用和经文的容器 */
            margin-top: 5px;
        }

        /* --- 新增：引用悬停预览样式 --- */
        .reference-hover {
            position: relative;
            display: inline-block;
            cursor: pointer;
            border-bottom: 1px dashed #4CAF50;
            padding: 0 2px;
        }

        .reference-hover:hover {
            background-color: #f0fff0;
        }

        .verse-preview {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: #fff;
            border: 1px solid #ccc;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: 4px;
            width: 300px;
            z-index: 100;
            font-style: italic;
            color: #155724;
            max-height: 200px;
            overflow-y: auto;
        }

        /* 为所有标签页添加悬停预览效果 */
        .reference-hover:hover .verse-preview {
            display: block;
        }

        /* 修改悬停效果，确保在contenteditable区域内也能正常工作 */
        #hoverContent .reference-hover:hover .verse-preview {
            display: block;
        }

        .verse-preview-header {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
        }

        .verse-preview-text {
            line-height: 1.5;
        }

        .verse-preview-not-found {
            color: #721c24;
            font-style: italic;
        }

        /* 为悬停阅读标签页添加特殊样式 */
        #hoverContent {
            position: relative;
            height: calc(100vh - 350px);
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 15px;
            border: 1px solid #ccc;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: "Courier New", Courier, monospace;
            font-size: 0.9em;
            line-height: 1.5;
            width: 100%;
            box-sizing: border-box;
            cursor: default;
            /* 添加默认光标样式，表明不可编辑 */
        }

        /* 移除不再需要的可编辑样式 */
        #hoverContent[contenteditable=true] {
            outline: none;
            border: 1px solid #ccc;
            padding: 15px;
        }

        #hoverContent[contenteditable=true]:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
        }

        /* --- 添加：自动修正样式 --- */
        .auto-corrected {
            text-decoration: underline dotted #FFA500;
            position: relative;
        }

        .correction-note {
            display: none;
            position: absolute;
            top: -25px;
            left: 0;
            background-color: #fff8e5;
            border: 1px solid #FFC107;
            color: #856404;
            font-size: 12px;
            padding: 3px 6px;
            border-radius: 3px;
            z-index: 100;
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .auto-corrected:hover .correction-note {
            display: block;
        }

        .scripture-invalid {
            color: #856404;
            background-color: #fff8e5;
            border-left: 3px solid #FFC107;
            padding: 5px 10px;
            margin-left: 1em;
            margin-top: 2px;
            margin-bottom: 5px;
            display: block;
            font-style: italic;
            font-size: 0.95em;
            border-radius: 3px;
        }

        /* --- 新增：响应式设计样式 --- */
        @media (max-width: 768px) {
            body {
                padding: 10px;
                font-size: 16px;
                /* 移动端字体稍大，便于阅读 */
            }

            h1 {
                font-size: 1.5em;
                text-align: center;
            }

            .tab-buttons {
                flex-direction: column;
                gap: 5px;
            }

            .tab-button {
                width: 100%;
                text-align: center;
                padding: 12px 5px;
                font-size: 16px;
            }

            .content-container {
                flex-direction: column;
                gap: 15px;
            }

            .content-panel {
                width: 100%;
            }

            .content-panel h2 {
                font-size: 1.2em;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .toggle-original-btn {
                margin-left: 0;
                align-self: flex-end;
            }

            #output,
            #originalContent,
            #formatOutput,
            #verseOutput,
            #formatOriginal,
            #verseOriginal,
            #hoverOriginal,
            #hoverContent {
                height: 300px;
                /* 移动端高度固定 */
                font-size: 16px;
            }

            .file-input-section {
                padding: 10px;
            }

            input[type="file"] {
                width: 100%;
                margin-top: 5px;
            }

            .copy-btn,
            .download-btn {
                width: 100%;
                margin: 5px 0;
                padding: 12px 16px;
                font-size: 16px;
            }

            /* 调整悬停预览在移动端的位置和大小 */
            .verse-preview {
                width: 90vw;
                max-width: 300px;
                left: 50%;
                transform: translateX(-50%);
            }

            /* 确保移动端表单元素足够大，便于触摸 */
            input[type="checkbox"] {
                width: 20px;
                height: 20px;
                vertical-align: middle;
            }

            label {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }

            /* 状态指示器在移动端的优化 */
            .loading-indicator,
            .success-indicator,
            .error-indicator {
                display: flex !important;
                flex-direction: row;
                align-items: center;
                padding: 10px;
                margin: 8px 0;
                border-radius: 6px;
                font-size: 14px;
            }

            /* 批量处理区域优化 */
            #batchFilesSection {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
            }

            #batchFilesList {
                max-height: 200px;
                overflow-y: auto;
                background-color: white;
                padding: 10px;
                border-radius: 6px;
                border: 1px solid #dee2e6;
                font-size: 14px;
            }

            /* 进度条在移动端的优化 */
            .progress-bar {
                margin: 15px 0;
                height: 8px;
            }
        }

        /* 针对更小屏幕的优化 */
        @media (max-width: 480px) {
            body {
                padding: 5px;
            }

            .file-input-section {
                margin-bottom: 10px;
            }

            #jsonStatus,
            #hoverJsonStatus,
            #verseJsonStatus {
                display: block;
                margin-top: 5px;
                margin-left: 0;
            }
        }

        /* 导出HTML功能样式 */
        .export-html-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .export-html-modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow: auto;
            position: relative;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .export-html-close {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #666;
        }

        .export-html-textarea {
            width: 100%;
            height: 300px;
            margin: 10px 0;
            font-family: monospace;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }

        .export-html-btn-group {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }

        .export-html-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: white;
        }

        .export-html-copy-btn {
            background-color: #4CAF50;
        }

        .export-html-download-btn {
            background-color: #2196F3;
        }

        /* 进度指示器样式 */
        .progress-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid #ddd;
            padding: 15px 20px;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .progress-container.show {
            display: block;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .progress-title {
            font-weight: bold;
            color: #333;
        }

        .progress-cancel {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .progress-cancel:hover {
            background-color: #c82333;
        }

        .progress-bar-container {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }

        .progress-stats {
            display: flex;
            gap: 15px;
        }

        .progress-stat {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .progress-stat-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .progress-stat-icon.processing {
            background-color: #ffc107;
        }

        .progress-stat-icon.completed {
            background-color: #28a745;
        }

        .progress-stat-icon.memory {
            background-color: #17a2b8;
        }

        /* 性能监控面板样式 */
        .performance-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 999;
            display: none;
        }

        .performance-panel.show {
            display: block;
        }

        .performance-panel h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        .performance-metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .performance-metric-label {
            color: #ccc;
        }

        .performance-metric-value {
            color: #4CAF50;
        }

        /* 大文本处理警告样式 */
        .large-text-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            display: none;
        }

        .large-text-warning.show {
            display: block;
        }

        .large-text-warning h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }

        .large-text-warning p {
            margin: 0 0 10px 0;
            color: #856404;
        }

        .large-text-options {
            display: flex;
            gap: 10px;
        }

        .large-text-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .large-text-btn.primary {
            background-color: #007bff;
            color: white;
        }

        .large-text-btn.secondary {
            background-color: #6c757d;
            color: white;
        }
    </style>

    <!-- 引入圣经数据文件 -->
    <script src="bib-data.js"></script>
</head>

<body>
    <!-- 进度指示器 -->
    <div id="progressContainer" class="progress-container">
        <div class="progress-header">
            <div class="progress-title">正在处理大文本...</div>
            <button id="progressCancel" class="progress-cancel">取消</button>
        </div>
        <div class="progress-bar-container">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        <div class="progress-info">
            <div class="progress-stats">
                <div class="progress-stat">
                    <div class="progress-stat-icon processing"></div>
                    <span id="progressCurrent">0</span> / <span id="progressTotal">0</span> 块
                </div>
                <div class="progress-stat">
                    <div class="progress-stat-icon memory"></div>
                    <span id="progressMemory">0</span> MB
                </div>
                <div class="progress-stat">
                    <div class="progress-stat-icon completed"></div>
                    <span id="progressTime">0</span> ms
                </div>
            </div>
            <div id="progressPercent">0%</div>
        </div>
    </div>

    <!-- 性能监控面板 -->
    <div id="performancePanel" class="performance-panel">
        <h4>⚡ 性能监控</h4>
        <div class="performance-metric">
            <span class="performance-metric-label">处理速度:</span>
            <span class="performance-metric-value" id="perfSpeed">0 行/秒</span>
        </div>
        <div class="performance-metric">
            <span class="performance-metric-label">内存使用:</span>
            <span class="performance-metric-value" id="perfMemory">0 MB</span>
        </div>
        <div class="performance-metric">
            <span class="performance-metric-label">Worker状态:</span>
            <span class="performance-metric-value" id="perfWorkers">0/0</span>
        </div>
        <div class="performance-metric">
            <span class="performance-metric-label">缓存命中:</span>
            <span class="performance-metric-value" id="perfCache">0%</span>
        </div>
    </div>

    <!-- 大文本处理警告 -->
    <div id="largeTextWarning" class="large-text-warning">
        <h4>⚠️ 大文本检测</h4>
        <p>检测到大量文本内容，建议使用优化处理模式以获得更好的性能。</p>
        <div class="large-text-options">
            <button id="enableOptimizedProcessing" class="large-text-btn primary">启用优化处理</button>
            <button id="continueNormalProcessing" class="large-text-btn secondary">继续普通处理</button>
        </div>
    </div>

    <h1>圣经引用格式化与经文查询工具</h1>


    <!-- 🔧 新增：全局设置选项 -->
    <div style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0; border-radius: 4px;">
        <h3 style="margin: 0 0 10px 0; color: #0066cc;">⚙️ 显示设置</h3>
        <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
            <div>
                <label style="font-weight: bold; margin-right: 10px;">经文引用格式：</label>
                <select id="referenceFormatSelect" style="padding: 5px; border: 1px solid #ccc; border-radius: 3px;">
                    <option value="space">带空格 (创 1:1)</option>
                    <option value="nospace">无空格 (创1:1)</option>
                </select>
            </div>
            <div style="color: #666; font-size: 0.9em;">
                选择经文引用在显示时的格式
            </div>
        </div>
    </div>

    <div class="tab-container">
        <div class="tab-buttons">
            <button class="tab-button active" data-tab="one-step">一步完成</button>
            <button class="tab-button" data-tab="format-step">步骤1：格式化引用</button>
            <button class="tab-button" data-tab="verse-step">步骤2：查找经文</button>
            <button class="tab-button" data-tab="hover-read">悬停阅读</button>
        </div>

        <!-- Tab1: 一步完成（原有功能） -->
        <div id="one-step" class="tab-content active">
            <div class="file-input-section">
                <label for="jsonFileInput">1. 经文数据状态 (已内嵌，可选择覆盖):</label>
                <input type="file" id="jsonFileInput" accept=".json" style="display: none;">
                <button onclick="document.getElementById('jsonFileInput').click()"
                    style="padding: 5px 10px; background-color: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">手动加载其他JSON文件</button>
                <span id="jsonStatus">正在加载内嵌数据...</span>
            </div>

            <div class="file-input-section">
                <h3>高级选项</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 10px;">
                    <label>
                        <input type="checkbox" id="enableSmartCorrection" checked>
                        启用智能纠错
                    </label>
                    <label>
                        <input type="checkbox" id="showCorrectionNotes" checked>
                        显示修正提示
                    </label>
                    <label>
                        <input type="checkbox" id="strictVerseValidation" checked>
                        严格验证节号
                    </label>
                </div>
            </div>

            <div class="file-input-section">
                <label for="txtFileInput">2. 选择要处理的 TXT 大纲文件:</label>
                <input type="file" id="txtFileInput" accept=".txt" multiple>
                <span id="batchStatus"></span>
            </div>

            <div class="file-input-section" id="batchFilesSection" style="display: none;">
                <h3>批量处理文件列表</h3>
                <div id="batchFilesList"></div>
                <div id="batchProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="batchProgressFill"></div>
                    </div>
                    <div id="batchProgressText"
                        style="text-align: center; margin-top: 5px; font-size: 0.9em; color: #666;"></div>
                </div>
                <button id="processBatchButton" class="copy-btn">开始批量处理</button>
            </div>

            <div class="content-container">
                <div class="content-panel">
                    <h2>原始 TXT 文件内容:</h2>
                    <textarea id="originalContent" placeholder="请先加载JSON，然后选择TXT文件或直接在此输入内容..."></textarea>
                </div>

                <div class="content-panel">
                    <h2 style="display: flex; align-items: center; justify-content: space-between;">
                        <span>处理结果 (包含经文):</span>
                        <button class="toggle-original-btn" id="toggleOutputOriginalButton"
                            data-state="visible">隐藏原文</button>
                    </h2>
                    <pre id="output">结果将显示在这里...</pre>
                </div>
            </div>
            <div style="text-align: center; margin: 20px 0;">
                <button id="copyButton" class="copy-btn">复制结果</button>
            </div>
        </div>

        <!-- Tab2: 格式化步骤 -->
        <div id="format-step" class="tab-content">
            <div class="file-input-section">
                <label for="formatTxtInput">选择要格式化的 TXT 文件:</label>
                <input type="file" id="formatTxtInput" accept=".txt">
            </div>

            <div class="content-container">
                <div class="content-panel">
                    <h2>原始 TXT 文件内容:</h2>
                    <textarea id="formatOriginal" placeholder="请选择TXT文件或直接在此输入内容..."></textarea>
                </div>

                <div class="content-panel">
                    <h2 style="display: flex; align-items: center; justify-content: space-between;">
                        <span>格式化结果:</span>
                        <button class="toggle-original-btn" id="toggleFormatOutputOriginalButton"
                            data-state="visible">隐藏原文</button>
                    </h2>
                    <pre id="formatOutput">格式化结果将显示在这里...</pre>
                </div>
            </div>
            <div style="text-align: center; margin: 20px 0;">
                <button id="copyFormatButton" class="copy-btn">复制结果</button>
                <button id="downloadFormatButton" class="download-btn">下载格式化结果</button>
            </div>
        </div>

        <!-- Tab3: 查找经文步骤 -->
        <div id="verse-step" class="tab-content">
            <div class="file-input-section">
                <label for="verseJsonInput">1. 经文数据状态 (已内嵌，可选择覆盖):</label>
                <input type="file" id="verseJsonInput" accept=".json" style="display: none;">
                <button onclick="document.getElementById('verseJsonInput').click()"
                    style="padding: 5px 10px; background-color: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">手动加载其他JSON文件</button>
                <span id="verseJsonStatus">正在加载内嵌数据...</span>
            </div>

            <div class="file-input-section">
                <label for="verseTxtInput">2. 选择格式化后的引用文件:</label>
                <input type="file" id="verseTxtInput" accept=".txt">
            </div>

            <div class="content-container">
                <div class="content-panel">
                    <h2>引用文件内容:</h2>
                    <textarea id="verseOriginal"
                        placeholder="请先加载JSON，然后选择TXT文件或直接在此输入内容（例如：罗 12:2 或 罗 5:3-5）..."></textarea>
                </div>

                <div class="content-panel">
                    <h2 style="display: flex; align-items: center; justify-content: space-between;">
                        <span>查经结果:</span>
                        <button class="toggle-original-btn" id="toggleVerseOutputOriginalButton"
                            data-state="visible">隐藏原文</button>
                    </h2>
                    <pre id="verseOutput">经文将显示在这里...</pre>
                </div>
            </div>
            <div style="text-align: center; margin: 20px 0;">
                <button id="copyVerseButton" class="copy-btn">复制结果</button>
            </div>
        </div>

        <!-- Tab4: 悬停阅读模式 -->
        <div id="hover-read" class="tab-content">
            <div class="file-input-section">
                <label for="hoverJsonInput">1. 经文数据状态 (已内嵌，可选择覆盖):</label>
                <input type="file" id="hoverJsonInput" accept=".json" style="display: none;">
                <button onclick="document.getElementById('hoverJsonInput').click()"
                    style="padding: 5px 10px; background-color: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">手动加载其他JSON文件</button>
                <span id="hoverJsonStatus">正在加载内嵌数据...</span>
            </div>

            <div class="file-input-section">
                <label for="hoverTxtInput">2. 选择要阅读的文本文件:</label>
                <input type="file" id="hoverTxtInput" accept=".txt">
            </div>

            <div class="content-container">
                <div class="content-panel">
                    <h2>原始 TXT 文件内容:</h2>
                    <textarea id="hoverOriginal" placeholder="请先加载JSON，然后选择TXT文件或直接在此输入内容..."></textarea>
                </div>

                <div class="content-panel">
                    <h2>处理结果 (带悬停效果):</h2>
                    <div id="hoverContent">结果将显示在这里...</div>
                    <button class="fullscreen-btn" id="hoverFullscreenBtn">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
                        </svg>
                        全屏
                    </button>
                </div>
            </div>
            <div style="text-align: center; margin: 20px 0;">
                <button id="exportHtmlButton" class="download-btn" style="background-color: #9C27B0;">导出HTML</button>
            </div>
        </div>
    </div>

    <!-- 导出HTML模态框 -->
    <div id="exportHtmlModal" class="export-html-modal">
        <div class="export-html-modal-content">
            <span class="export-html-close">&times;</span>
            <h3>导出HTML</h3>
            <p>以下是包含所有格式和效果的HTML代码:</p>
            <textarea id="exportHtmlTextarea" class="export-html-textarea" readonly></textarea>
            <div class="export-html-btn-group">
                <button id="exportHtmlCopyBtn" class="export-html-btn export-html-copy-btn">复制HTML</button>
                <button id="exportHtmlDownloadBtn" class="export-html-btn export-html-download-btn">下载HTML文件</button>
            </div>
        </div>
    </div>

    <script>

        // --- 引用格式配置管理器 ---
        const ReferenceFormatManager = {
            // 默认格式：带空格
            currentFormat: 'space',

            // 初始化格式设置
            init() {
                const selectElement = document.getElementById('referenceFormatSelect');
                if (selectElement) {
                    // 从localStorage加载保存的设置
                    const savedFormat = localStorage.getItem('referenceFormat') || 'space';
                    this.currentFormat = savedFormat;
                    selectElement.value = savedFormat;

                    // 监听格式变化
                    selectElement.addEventListener('change', (e) => {
                        this.currentFormat = e.target.value;
                        localStorage.setItem('referenceFormat', e.target.value);
                        console.log(`📝 引用格式已更改为: ${e.target.value === 'space' ? '带空格' : '无空格'}`);

                        // 如果当前有内容，重新处理
                        this.refreshAllContent();
                    });
                }
            },

            // 格式化引用字符串
            formatReference(book, chapter, verse) {
                if (this.currentFormat === 'nospace') {
                    return `${book}${chapter}:${verse}`;
                } else {
                    return `${book} ${chapter}:${verse}`;
                }
            },

            // 刷新所有标签页的内容
            refreshAllContent() {
                // 触发所有输入框的input事件，重新处理内容
                const inputElements = [
                    'originalContent',    // 一步完成
                    'formatOriginal',     // 格式化引用
                    'verseOriginal',      // 查找经文
                    'hoverOriginal'       // 悬停阅读
                ];

                inputElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element && element.value.trim()) {
                        element.dispatchEvent(new Event('input'));
                    }
                });

                // 特殊处理悬停阅读
                const hoverContent = document.getElementById('hoverContent');
                const hoverOriginal = document.getElementById('hoverOriginal');
                if (hoverOriginal && hoverOriginal.value.trim() &&
                    hoverContent && hoverContent.textContent.trim() !== '请先加载JSON，然后选择TXT文件或直接在此输入内容...') {
                    if (typeof processHoverReadContent === 'function') {
                        processHoverReadContent(hoverOriginal.value);
                    }
                }
            }
        };

        // --- 类型检查和验证工具模块 ---
        const TypeValidator = {
            // 检查是否为有效的字符串
            isValidString(value, allowEmpty = false) {
                if (typeof value !== 'string') return false;
                return allowEmpty || value.trim().length > 0;
            },

            // 检查是否为有效的数字
            isValidNumber(value, min = null, max = null) {
                if (typeof value !== 'number' || isNaN(value)) return false;
                if (min !== null && value < min) return false;
                if (max !== null && value > max) return false;
                return true;
            },

            // 检查是否为有效的对象
            isValidObject(value, requiredKeys = []) {
                if (!value || typeof value !== 'object' || Array.isArray(value)) return false;
                return requiredKeys.every(key => key in value);
            },

            // 检查是否为有效的数组
            isValidArray(value, minLength = 0) {
                if (!Array.isArray(value)) return false;
                return value.length >= minLength;
            },

            // 检查是否为有效的函数
            isValidFunction(value) {
                return typeof value === 'function';
            },

            // 检查是否为有效的DOM元素
            isValidElement(value) {
                return value instanceof Element;
            },

            // 检查是否为有效的文件对象
            isValidFile(value) {
                return value instanceof File;
            },

            // 检查是否为有效的经文引用格式（增强版 - 支持多种格式）
            isValidVerseReference(ref) {
                if (!this.isValidString(ref)) return false;

                // 支持多种经文引用格式
                const formats = [
                    REGEX.VERSE_KEY,                        // 标准格式: "创 1:1"
                    /^(.+)\.(\d+)\.(\d+)$/,                 // 点分格式: "Genesis.1.1"
                    /^(.+)_(\d+)_(\d+)$/,                   // 下划线格式: "Genesis_1_1"
                    /^(.+)-(\d+)-(\d+)$/,                   // 连字符格式: "Genesis-1-1"
                    /^(.+)\s+(\d+):(\d+)$/,                 // 空格格式: "Genesis 1:1"
                    /^(.+)(\d+):(\d+)$/,                    // 无空格格式: "Genesis1:1"
                    /^(\d+)_(\d+)_(.+)$/,                   // 章节在前: "1_1_Genesis"
                    /^(\w+)(\d+)v(\d+)$/                    // 版本格式: "Gen1v1"
                ];

                return formats.some(format => format.test(ref));
            },

            // 🔧 已删除JSON验证代码

            // 参数验证装饰器
            validateParams(validations) {
                return function (target, propertyName, descriptor) {
                    const method = descriptor.value;
                    descriptor.value = function (...args) {
                        for (let i = 0; i < validations.length; i++) {
                            const validation = validations[i];
                            const arg = args[i];

                            if (!validation.validator(arg)) {
                                throw new TypeError(`参数 ${i + 1} (${validation.name}) 验证失败: ${validation.message}`);
                            }
                        }
                        return method.apply(this, args);
                    };
                };
            }
        };

        // --- 错误处理工具模块 ---
        const ErrorHandler = {
            // 错误类型枚举
            ErrorTypes: {
                VALIDATION: 'ValidationError',
                DATA_LOADING: 'DataLoadingError',
                FILE_PROCESSING: 'FileProcessingError',
                NETWORK: 'NetworkError',
                STORAGE: 'StorageError',
                PARSING: 'ParsingError'
            },

            // 创建自定义错误
            createError(type, message, originalError = null) {
                const error = new Error(message);
                error.name = type;
                error.originalError = originalError;
                error.timestamp = new Date().toISOString();
                return error;
            },

            // 安全执行函数，捕获并处理错误
            safeExecute(fn, context = 'Unknown', fallbackValue = null) {
                try {
                    return fn();
                } catch (error) {
                    console.error(`[${context}] 执行出错:`, error);
                    DataManager.showUserFriendlyError(error, context);
                    return fallbackValue;
                }
            },

            // 异步安全执行
            async safeExecuteAsync(fn, context = 'Unknown', fallbackValue = null) {
                try {
                    return await fn();
                } catch (error) {
                    console.error(`[${context}] 异步执行出错:`, error);
                    DataManager.showUserFriendlyError(error, context);
                    return fallbackValue;
                }
            },

            // 验证并执行
            validateAndExecute(validations, fn, context = 'Unknown') {
                try {
                    // 执行所有验证
                    validations.forEach((validation, index) => {
                        if (!validation.condition) {
                            throw this.createError(
                                this.ErrorTypes.VALIDATION,
                                validation.message || `验证 ${index + 1} 失败`
                            );
                        }
                    });

                    return fn();
                } catch (error) {
                    console.error(`[${context}] 验证或执行失败:`, error);
                    DataManager.showUserFriendlyError(error, context);
                    return null;
                }
            }
        };

        // --- 数据管理模块 ---
        const DataManager = {
            bibleData: null,
            verseCountData: {},

            // 加载圣经数据（无验证版本）
            loadBibleData(data) {
                try {
                    // 基本的空值检查
                    if (!data || typeof data !== 'object') {
                        console.error('❌ 数据验证失败:', { data: data, type: typeof data });
                        throw new Error('数据为空或不是有效的对象');
                    }

                    this.bibleData = data;
                    this.buildVerseCountData(data);
                    console.log('✅ 经文数据加载完成:', Object.keys(data).length, '条记录');

                    const dataSize = Object.keys(data).length;
                    this.updateAllStatusElements(
                        `JSON 数据已成功加载 (${dataSize} 条经文)`,
                        '#28a745',
                        'success'
                    );

                    console.log(`✅ 经文数据加载成功: ${dataSize} 条记录`);
                    return true;
                } catch (error) {
                    console.error('❌ 数据加载失败:', error);
                    throw error;
                }
            },

            // 🔧 已删除JSON预处理代码

            // 🔧 已删除智能解析代码

            // 从缓存加载数据（优化版 - 支持版本控制和性能监控）
            loadFromCache() {
                const startTime = performance.now();

                // 尝试加载新版本缓存
                let cachedData = localStorage.getItem('bibleDataV2');
                let isNewFormat = true;

                // 如果新版本不存在，尝试旧版本
                if (!cachedData) {
                    cachedData = localStorage.getItem('bibleData');
                    isNewFormat = false;
                }

                if (!cachedData) return false;

                try {
                    let parsedCache, actualData, cacheTimestamp;

                    if (isNewFormat) {
                        // 新格式缓存
                        parsedCache = JSON.parse(cachedData);
                        actualData = parsedCache.data;
                        cacheTimestamp = parsedCache.timestamp;

                        // 检查版本兼容性
                        if (parsedCache.version !== '1.1') {
                            console.warn('缓存版本不兼容，将重新加载');
                            this.clearOldCache();
                            return false;
                        }
                    } else {
                        // 旧格式缓存
                        actualData = JSON.parse(cachedData);
                        cacheTimestamp = parseInt(localStorage.getItem('bibleDataTimestamp') || '0');
                    }

                    const now = Date.now();
                    const cacheAge = now - cacheTimestamp;
                    const maxAge = 86400000; // 1天

                    // 如果缓存未过期，使用缓存数据
                    if (cacheAge < maxAge) {
                        this.loadBibleData(actualData);

                        const loadTime = performance.now() - startTime;
                        const ageHours = Math.floor(cacheAge / 3600000);
                        this.updateAllStatusElements(
                            `JSON 数据已从缓存加载 (${loadTime.toFixed(1)}ms, ${ageHours}小时前)`,
                            '#28a745'
                        );

                        // 如果是旧格式，异步升级到新格式
                        if (!isNewFormat) {
                            setTimeout(() => this.cacheData(actualData), 100);
                        }

                        return true;
                    } else {
                        // 缓存已过期
                        console.log('缓存已过期，将重新加载');
                        this.clearOldCache();
                    }
                } catch (e) {
                    console.error('读取缓存数据失败:', e);
                    this.clearOldCache();
                }
                return false;
            },

            // 清理旧缓存
            clearOldCache() {
                localStorage.removeItem('bibleData');
                localStorage.removeItem('bibleDataTimestamp');
                localStorage.removeItem('bibleDataV2');
            },

            // 缓存数据到localStorage（优化版 - 支持压缩和版本控制）
            cacheData(data) {
                try {
                    const cacheVersion = '1.1'; // 缓存版本号
                    const cacheData = {
                        version: cacheVersion,
                        timestamp: Date.now(),
                        data: data,
                        compressed: false // 未来可以添加压缩支持
                    };

                    // 检查数据大小
                    const dataString = JSON.stringify(cacheData);
                    const sizeInMB = dataString.length / (1024 * 1024);

                    if (sizeInMB > 5) { // 如果超过5MB，考虑不缓存
                        console.warn(`数据过大 (${sizeInMB.toFixed(2)}MB)，跳过缓存`);
                        return;
                    }

                    localStorage.setItem('bibleDataV2', dataString);
                    console.log(`数据已缓存 (${sizeInMB.toFixed(2)}MB)`);
                } catch (e) {
                    console.warn('无法缓存数据到localStorage:', e);
                    // 如果是存储空间不足，尝试清理旧缓存
                    if (e.name === 'QuotaExceededError') {
                        this.clearOldCache();
                    }
                }
            },

            // 更新所有状态元素（增强版）
            updateAllStatusElements(text, color, type = 'info') {
                const statusElements = ['jsonStatus', 'verseJsonStatus', 'hoverJsonStatus'];
                statusElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        // 清除之前的类
                        element.className = '';

                        // 根据类型添加相应的样式类
                        switch (type) {
                            case 'loading':
                                element.className = 'loading-indicator';
                                element.innerHTML = `<div class="loading-spinner"></div>${text}`;
                                break;
                            case 'success':
                                element.className = 'success-indicator';
                                element.innerHTML = `✓ ${text}`;
                                break;
                            case 'error':
                                element.className = 'error-indicator';
                                element.innerHTML = `✗ ${text}`;
                                break;
                            default:
                                element.textContent = text;
                                element.style.color = color;
                        }
                    }
                });
            },

            // 显示用户友好的错误消息
            showUserFriendlyError(error, context = '') {
                let userMessage = '';
                let technicalDetails = '';

                if (error.name === 'SyntaxError') {
                    userMessage = 'JSON文件格式不正确，请检查文件内容';
                    technicalDetails = `语法错误: ${error.message}`;
                } else if (error.name === 'TypeError') {
                    userMessage = '数据处理出现问题，可能是文件格式不兼容';
                    technicalDetails = `类型错误: ${error.message}`;
                } else if (error.name === 'ReferenceError') {
                    userMessage = '程序内部错误，请刷新页面重试';
                    technicalDetails = `引用错误: ${error.message}`;
                } else {
                    userMessage = '操作失败，请重试';
                    technicalDetails = error.message;
                }

                const fullMessage = context ? `${context}: ${userMessage}` : userMessage;

                this.updateAllStatusElements(fullMessage, '#dc3545', 'error');

                // 在控制台输出详细错误信息供开发者调试
                console.error(`${context} - ${technicalDetails}`, error);

                return fullMessage;
            },

            // 构建节数数据（增强版 - 添加错误处理和性能优化）
            buildVerseCountData(data) {
                return ErrorHandler.safeExecute(() => {
                    if (!TypeValidator.isValidObject(data)) {
                        throw ErrorHandler.createError(
                            ErrorHandler.ErrorTypes.VALIDATION,
                            '无效的数据对象'
                        );
                    }

                    this.verseCountData = {};
                    let processedCount = 0;
                    let errorCount = 0;

                    for (const key in data) {
                        try {
                            const match = key.match(REGEX.VERSE_KEY);
                            if (match) {
                                const book = match[1];
                                const chapter = parseInt(match[2], 10);
                                const verse = parseInt(match[3], 10);

                                // 验证解析结果
                                if (!TypeValidator.isValidString(book) ||
                                    !TypeValidator.isValidNumber(chapter, 1) ||
                                    !TypeValidator.isValidNumber(verse, 1)) {
                                    errorCount++;
                                    continue;
                                }

                                if (!this.verseCountData[book]) {
                                    this.verseCountData[book] = {};
                                }
                                if (!this.verseCountData[book][chapter]) {
                                    this.verseCountData[book][chapter] = 0;
                                }
                                this.verseCountData[book][chapter] = Math.max(this.verseCountData[book][chapter], verse);
                                processedCount++;
                            }
                        } catch (error) {
                            console.warn(`处理键 "${key}" 时出错:`, error);
                            errorCount++;
                        }
                    }

                    console.log(`📊 节数数据构建完成: ${processedCount} 个有效记录, ${errorCount} 个错误记录`);
                    return { processedCount, errorCount };
                }, '节数数据构建', { processedCount: 0, errorCount: 0 });
            },

            // 获取圣经数据
            getBibleData() {
                return this.bibleData;
            },

            // 检查数据是否已加载
            isDataLoaded() {
                return this.bibleData !== null;
            }
        };

        // 预编译常用正则表达式
        const REGEX = {
            // 书卷章节引用正则
            VERSE_KEY: /^(.+) (\d+):(\d+)$/,
            // 中文数字范围
            CN_NUM: '[一二三四五六七八九十百千〇零]+',
            // 阿拉伯数字范围
            AR_NUM_RANGE: '\\d+(?:[~～˜∽-]\\d+)?',
            // 单个阿拉伯数字
            SINGLE_AR_NUM: /^(\d+)$/,
            // 阿拉伯数字范围
            AR_NUM_RANGE_MATCH: /^(\d+)[~～˜∽-](\d+)$/,
            // 中文节号或范围
            CN_VERSE_RANGE: null, // 将在下面初始化
            // 章节分隔符
            CHAPTER_VERSE_SEPARATOR: /^(\d+)[:：]?(.+)$/,
            // 纯数字判断
            PURE_DIGIT: /^\d+$/,
            // 数字范围判断
            DIGIT_RANGE: /\d+[~～˜∽-]\d+/,
            // 列表项判断
            LOOKS_LIKE_LIST_PREV: /[\s\t　(（［【]/,
            LOOKS_LIKE_LIST_NEXT: /[\s\t　.、]/,
            // 书卷章节上下文判断
            CHAPTER_CONTEXT: /([一二三四五六七八九十百千〇零\d]+章)\S*$/,
            BOOK_CHAPTER_CONTEXT: /([a-zA-Z罗林加弗腓西帖提前后多门来雅彼约犹启创出利民申书士得撒王代拉尼斯伯诗箴传歌赛耶哀结但何珥摩俄拿弥鸿哈番该亚玛太可路徒]\s*\d+[:：])\S*$/i
        };

        // 在脚本开头定义，避免重复创建
        REGEX.CN_VERSE_RANGE = `${REGEX.CN_NUM}(?:(?:至|到|-|~)?${REGEX.CN_NUM})?`;
        REGEX.CN_NUM_ONLY = new RegExp(`^[${REGEX.CN_NUM}]+$`);
        REGEX.CHINESE_VERSE_RANGE = new RegExp(`^(${REGEX.CN_NUM})(?:(?:至|到|-|~)?(${REGEX.CN_NUM}))?$`);

        // --- Tab切换逻辑 ---
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                // 移除所有按钮和内容的 active 类
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                // 为当前点击的按钮和对应的内容添加 active 类
                button.classList.add('active');
                document.getElementById(button.dataset.tab).classList.add('active');
            });
        });

        // --- 核心处理逻辑 ---

        // 书卷简称映射表 (简体为主，包含一些繁体和常见变体到标准简体的映射)
        const bookMap = {
            // 旧约
            '创': '创', '出': '出', '利': '利', '民': '民', '申': '申',
            '书': '书', '士': '士', '得': '得', '撒上': '撒上', '撒下': '撒下',
            '王上': '王上', '王下': '王下', '代上': '代上', '代下': '代下',
            '拉': '拉', '尼': '尼', '斯': '斯', '伯': '伯', '诗': '诗',
            '箴': '箴', '传': '传', '歌': '歌', '赛': '赛', '耶': '耶',
            '哀': '哀', '结': '结', '但': '但', '何': '何', '珥': '珥',
            '摩': '摩', '俄': '俄', '拿': '拿', '弥': '弥', '鸿': '鸿',
            '哈': '哈', '番': '番', '该': '该', '亚': '亚', '玛': '玛',
            // 新约
            '太': '太', '可': '可', '路': '路', '约': '约', '徒': '徒',
            '罗': '罗', '林前': '林前', '林后': '林后', '加': '加', '弗': '弗',
            '腓': '腓', '西': '西', '帖前': '帖前', '帖后': '帖后',
            '提前': '提前', '提后': '提后', '多': '多', '门': '门',
            '来': '来', '雅': '雅', '彼前': '彼前', '彼后': '彼后',
            '约壹': '约壹', '约贰': '约贰', '约叁': '约叁', '犹': '犹',
            '启': '启',
            // 繁体及其他简称到标准简称的映射
            '創': '创', '書': '书', '詩': '诗', '傳': '传', '賽': '赛',
            '結': '结', '彌': '弥', '鴻': '鸿', '該': '该', '亞': '亚',
            '瑪': '玛', '羅': '罗', '林後': '林后', '帖後': '帖后',
            '提後': '提后', '來': '来', '猶': '犹', '啓': '启',
            '參': '' // "参" 本身不是书名，但在正则中用于匹配，这里设为空或移除
        };

        // 书卷章节限制数据
        const bookChapterLimits = {
            // 旧约
            '创': 50, '出': 40, '利': 27, '民': 36, '申': 34,
            '书': 24, '士': 21, '得': 4, '撒上': 31, '撒下': 24,
            '王上': 22, '王下': 25, '代上': 29, '代下': 36,
            '拉': 10, '尼': 13, '斯': 10, '伯': 42, '诗': 150,
            '箴': 31, '传': 12, '歌': 8, '赛': 66, '耶': 52,
            '哀': 5, '结': 48, '但': 12, '何': 14, '珥': 3,
            '摩': 9, '俄': 1, '拿': 4, '弥': 7, '鸿': 3,
            '哈': 3, '番': 3, '该': 2, '亚': 14, '玛': 4,
            // 新约
            '太': 28, '可': 16, '路': 24, '约': 21, '徒': 28,
            '罗': 16, '林前': 16, '林后': 13, '加': 6, '弗': 6,
            '腓': 4, '西': 4, '帖前': 5, '帖后': 3,
            '提前': 6, '提后': 4, '多': 3, '门': 1,
            '来': 13, '雅': 5, '彼前': 5, '彼后': 3,
            '约壹': 5, '约贰': 1, '约叁': 1, '犹': 1,
            '启': 22
        };

        // --- 文件处理模块（增强版 - 添加类型检查和错误处理） ---
        const FileManager = {
            // 统一的JSON文件加载处理（增强版）
            handleJsonFileLoad(file, callback) {
                return ErrorHandler.validateAndExecute([
                    {
                        condition: TypeValidator.isValidFile(file),
                        message: '无效的文件对象'
                    },
                    {
                        condition: !callback || TypeValidator.isValidFunction(callback),
                        message: '回调函数必须是有效的函数'
                    }
                ], () => {
                    // 显示加载状态
                    DataManager.updateAllStatusElements('正在读取JSON文件...', '#FFA500', 'loading');

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            // 显示解析状态
                            DataManager.updateAllStatusElements('正在解析JSON数据...', '#FFA500', 'loading');

                            const data = JSON.parse(e.target.result);

                            // 🔧 修改：跳过JSON格式验证，直接加载数据
                            console.log('⚠️ 已跳过JSON格式验证，直接加载数据');

                            DataManager.loadBibleData(data);
                            DataManager.cacheData(data);

                            // 显示成功状态
                            const dataSize = Object.keys(data).length;
                            DataManager.updateAllStatusElements(
                                `JSON数据加载成功 (${dataSize} 条经文)`,
                                '#28a745',
                                'success'
                            );

                            if (callback) callback(data);
                        } catch (error) {
                            DataManager.showUserFriendlyError(error, 'JSON文件加载');
                        }
                    };
                    reader.onerror = () => {
                        DataManager.showUserFriendlyError(
                            ErrorHandler.createError(
                                ErrorHandler.ErrorTypes.FILE_PROCESSING,
                                '无法读取文件，请检查文件是否损坏'
                            ),
                            '文件读取'
                        );
                    };
                    reader.readAsText(file);
                }, 'JSON文件加载');
            },

            // 统一的文本文件加载处理（增强版）
            handleTextFileLoad(file, callback) {
                return ErrorHandler.validateAndExecute([
                    {
                        condition: TypeValidator.isValidFile(file),
                        message: '无效的文件对象'
                    },
                    {
                        condition: !callback || TypeValidator.isValidFunction(callback),
                        message: '回调函数必须是有效的函数'
                    }
                ], () => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const content = e.target.result;
                            if (!TypeValidator.isValidString(content, true)) {
                                throw ErrorHandler.createError(
                                    ErrorHandler.ErrorTypes.FILE_PROCESSING,
                                    '文件内容为空或无效'
                                );
                            }
                            if (callback) callback(content);
                        } catch (error) {
                            DataManager.showUserFriendlyError(error, '文件内容处理');
                        }
                    };
                    reader.onerror = () => {
                        DataManager.showUserFriendlyError(
                            ErrorHandler.createError(
                                ErrorHandler.ErrorTypes.FILE_PROCESSING,
                                '文件读取失败，请检查文件是否损坏'
                            ),
                            '文件读取'
                        );
                    };
                    reader.readAsText(file);
                }, '文本文件加载');
            },

            // 批量文件处理（增强版）
            handleBatchFiles(files, callback) {
                return ErrorHandler.validateAndExecute([
                    {
                        condition: files && (files.length > 0 || TypeValidator.isValidArray(Array.from(files), 1)),
                        message: '文件列表为空或无效'
                    },
                    {
                        condition: !callback || TypeValidator.isValidFunction(callback),
                        message: '回调函数必须是有效的函数'
                    }
                ], () => {
                    const results = [];
                    let completed = 0;
                    let errors = 0;
                    const fileArray = Array.from(files);

                    fileArray.forEach((file, index) => {
                        // 验证单个文件
                        if (!TypeValidator.isValidFile(file)) {
                            console.warn(`文件 ${index + 1} 无效，跳过处理`);
                            results[index] = {
                                name: `文件${index + 1}`,
                                content: '',
                                error: '无效的文件对象',
                                success: false
                            };
                            completed++;
                            errors++;

                            if (completed === fileArray.length) {
                                if (callback) callback(results, { total: fileArray.length, errors });
                            }
                            return;
                        }

                        this.handleTextFileLoad(file, (content) => {
                            results[index] = {
                                name: file.name,
                                content: content,
                                success: true
                            };
                            completed++;

                            if (completed === fileArray.length) {
                                if (callback) callback(results, { total: fileArray.length, errors });
                            }
                        });
                    });
                }, '批量文件处理');
            }
        };

        // 书卷全称到标准简称的映射表
        const fullBookNameToAbbrMap = {
            '创世记': '创',
            '出埃及记': '出', '出埃及': '出',
            '利未记': '利',
            '民数记': '民',
            '申命记': '申',
            '约书亚记': '书', '约书亚': '书',
            '士师记': '士',
            '路得记': '得', '路得': '得',
            '撒母耳记上': '撒上', '撒母耳上': '撒上',
            '撒母耳记下': '撒下', '撒母耳下': '撒下',
            '列王纪上': '王上', '列王纪下': '王下',
            '历代志上': '代上', '历代志下': '代下',
            '以斯拉记': '拉',
            '尼希米记': '尼', '尼希米': '尼',
            '以斯帖记': '斯', '以斯帖': '斯',
            '约伯记': '伯', '约伯': '伯',
            '诗篇': '诗', '箴言': '箴',
            '传道书': '传', '传道': '传',
            '雅歌': '歌',
            '以赛亚书': '赛', '以赛亚': '赛',
            '耶利米书': '耶', '耶利米': '耶',
            '耶利米哀歌': '哀', '哀歌': '哀',
            '以西结书': '结', '以西结': '结',
            '但以理书': '但', '但以理': '但',
            '何西阿书': '何', '何西阿': '何',
            '约珥书': '珥', '约珥': '珥',
            '阿摩司书': '摩', '阿摩司': '摩',
            '俄巴底亚书': '俄', '俄巴底亚': '俄',
            '约拿书': '拿', '约拿': '拿',
            '弥迦书': '弥', '弥迦': '弥',
            '那鸿书': '鸿', '那鸿': '鸿',
            '哈巴谷书': '哈', '哈巴谷': '哈',
            '西番雅书': '番', '西番雅': '番',
            '哈该书': '该', '哈该': '该',
            '撒迦利亚书': '亚', '撒迦利亚': '亚',
            '玛拉基书': '玛', '玛拉基': '玛',
            '马太福音': '太', '马太': '太',
            '马可福音': '可', '马可': '可',
            '路加福音': '路', '路加': '路',
            '约翰福音': '约', '约翰': '约',
            '使徒行传': '徒', '行传': '徒',
            '罗马书': '罗', '罗马': '罗',
            '哥林多前书': '林前', '哥林多后书': '林后',
            '加拉太书': '加', '加拉太': '加',
            '以弗所书': '弗', '以弗所': '弗',
            '腓立比书': '腓', '腓立比': '腓',
            '歌罗西书': '西', '歌罗西': '西',
            '帖撒罗尼迦前书': '帖前', '帖撒罗尼迦后书': '帖后',
            '提摩太前书': '提前', '提摩太后书': '提后',
            '提多书': '多', '提多': '多',
            '腓利门书': '门', '腓利门': '门',
            '希伯来书': '来', '希伯来': '来',
            '雅各书': '雅', '雅各': '雅',
            '彼得前书': '彼前', '彼得后书': '彼后',
            '约翰一书': '约壹', '约翰壹书': '约壹',
            '约翰二书': '约贰', '约翰贰书': '约贰',
            '约翰三书': '约叁', '约翰叁书': '约叁',
            '犹大书': '犹', '犹大': '犹',
            '启示录': '启',
        };

        // 只有一章的圣经书卷集合
        const singleChapterBooks = new Set(['俄', '门', '约贰', '约叁', '犹']);

        // --- 数字转换工具模块 ---
        const NumberConverter = {
            // 缓存机制
            _cache: new Map(),

            // 清除缓存
            clearCache() {
                this._cache.clear();
            },

            // 将中文数字（混合单位）转换为阿拉伯数字（增强版）
            chineseToArabic(numStr) {
                return ErrorHandler.safeExecute(() => {
                    // 参数验证
                    if (!TypeValidator.isValidString(numStr)) {
                        return 0;
                    }

                    // 检查缓存
                    if (this._cache.has(numStr)) {
                        return this._cache.get(numStr);
                    }

                    const result = this._convertChineseToArabic(numStr);
                    this._cache.set(numStr, result);
                    return result;
                }, '中文数字转换', 0);
            },

            // 内部转换逻辑
            _convertChineseToArabic(numStr) {
                // 添加缓存机制
                if (!chineseToArabic.cache) {
                    chineseToArabic.cache = new Map();
                }

                // 检查缓存
                if (chineseToArabic.cache.has(numStr)) {
                    return chineseToArabic.cache.get(numStr);
                }

                const map = { // 数字字符映射
                    '〇': 0, '零': 0, '一': 1, '二': 2, '三': 3, '四': 4,
                    '五': 5, '六': 6, '七': 7, '八': 8, '九': 9
                };
                const units = { '十': 10, '百': 100, '千': 1000 }; // 单位映射
                if (!numStr) return 0;
                numStr = String(numStr); // 确保是字符串

                // 单个中文字符直接转换
                if (numStr.length === 1 && map.hasOwnProperty(numStr)) {
                    const result = map[numStr];
                    chineseToArabic.cache.set(numStr, result);
                    return result;
                }

                // 检查是否为纯中文数字序列 (如 "一二三")
                let containsOnlyDigits = true;
                for (let i = 0; i < numStr.length; i++) {
                    if (!map.hasOwnProperty(numStr[i])) {
                        containsOnlyDigits = false;
                        break;
                    }
                }
                if (containsOnlyDigits && numStr.length > 1) {
                    let v = 0;
                    for (let i = 0; i < numStr.length; i++) v = v * 10 + map[numStr[i]];
                    chineseToArabic.cache.set(numStr, v);
                    return v;
                }

                // 处理 "十", "十一", "二十", "二十一" 等情况
                if (numStr === '十') {
                    chineseToArabic.cache.set(numStr, 10);
                    return 10;
                }
                if (numStr.startsWith('十')) {
                    if (numStr.length === 1) {
                        chineseToArabic.cache.set(numStr, 10);
                        return 10;
                    }
                    if (map.hasOwnProperty(numStr[1])) {
                        const result = 10 + map[numStr[1]];
                        chineseToArabic.cache.set(numStr, result);
                        return result;
                    } // "十一" 到 "十九"
                    chineseToArabic.cache.set(numStr, 10);
                    return 10; // 仅 "十"
                }

                let totalValue = 0;
                let sectionValue = 0;
                let digitValue = 0;
                for (let i = 0; i < numStr.length; i++) {
                    const char = numStr[i];
                    if (map.hasOwnProperty(char)) { // 是数字字符
                        digitValue = map[char];
                        if (i === numStr.length - 1) sectionValue += digitValue; // 如果是最后一个字符，直接加到段值
                    } else if (units.hasOwnProperty(char)) { // 是单位字符
                        const unitMultiplier = units[char];
                        if (digitValue === 0) { // 处理 "一百", "一千" 或 "百", "千" (视为一百, 一千)
                            if (sectionValue === 0 && i === 0) sectionValue = 1 * unitMultiplier; // "百" -> 100
                            else if (unitMultiplier > 10 && digitValue === 0) sectionValue += 1 * unitMultiplier; // "一百零" 中的 "一百"
                            else sectionValue = (sectionValue === 0 ? 1 : sectionValue) * unitMultiplier;
                        } else {
                            sectionValue += digitValue * unitMultiplier;
                        }
                        digitValue = 0; // 单位处理完后，数字重置
                    }
                    if (char === '零' || char === '〇') { // 处理零
                        // 如果零后面是单位 (如 "一百零一"), digitValue 已经是0，继续
                        // 否则，普通的零，digitValue 设为0
                        digitValue = 0;
                    }
                }
                totalValue += sectionValue;
                return totalValue;
            },

            // 解析中文数字表示的节号或节范围 (如 "一" 或 "一至三")
            parseChineseVerseRange(chineseVersePart) {
                return ErrorHandler.safeExecute(() => {
                    if (!TypeValidator.isValidString(chineseVersePart)) {
                        return null;
                    }
                    // 使用预定义的正则表达式
                    const parts = chineseVersePart.match(REGEX.CHINESE_VERSE_RANGE);

                    if (!parts) return null; // 不匹配格式

                    const startVerse = this.chineseToArabic(parts[1]);
                    if (parts[2]) { // 如果是范围 (如 "一至三")
                        const endVerse = this.chineseToArabic(parts[2]);
                        if (startVerse > 0 && endVerse > 0 && startVerse <= endVerse) {
                            return `${startVerse}-${endVerse}`; // 返回 "数字-数字" 格式
                        }
                        return null; // 无效范围
                    } else { // 如果是单个节号
                        if (startVerse > 0) {
                            return String(startVerse); // 返回 "数字" 格式
                        }
                        return null; // 无效节号
                    }
                }, '中文节号范围解析', null);
            }
        };

        // --- HTML工具模块 ---
        const HtmlUtils = {
            // 用于在 pre/div 中显示时转义 HTML 特殊字符
            escapeHtml(unsafe) {
                return ErrorHandler.safeExecute(() => {
                    if (!TypeValidator.isValidString(unsafe, true)) {
                        return '';
                    }
                    return unsafe
                        .replace(/&/g, "&amp;")
                        .replace(/</g, "&lt;")
                        .replace(/>/g, "&gt;")
                        .replace(/"/g, "&quot;")
                        .replace(/'/g, "&#039;");
                }, 'HTML转义', '');
            }
        };

        // 为了向后兼容，保留全局函数
        function chineseToArabic(numStr) {
            return NumberConverter.chineseToArabic(numStr);
        }

        function parseChineseVerseRange(chineseVersePart) {
            return NumberConverter.parseChineseVerseRange(chineseVersePart);
        }

        function escapeHtml(unsafe) {
            return HtmlUtils.escapeHtml(unsafe);
        }

        // 用于在 pre/div 中显示时转义 HTML 特殊字符
        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        // --- 正则表达式优化管理模块 ---
        const RegexOptimizer = {
            // 全局正则缓存
            _globalCache: new Map(),
            _cacheMaxSize: 100,
            _hitCount: 0,
            _missCount: 0,

            // 创建优化的正则表达式
            createOptimizedRegex(pattern, flags = '', cacheKey = null) {
                const key = cacheKey || `${pattern}_${flags}`;

                // 检查缓存
                if (this._globalCache.has(key)) {
                    this._hitCount++;
                    return this._globalCache.get(key);
                }

                this._missCount++;

                // 创建新的正则表达式
                const regex = new RegExp(pattern, flags);

                // 缓存管理
                if (this._globalCache.size >= this._cacheMaxSize) {
                    // 删除最旧的缓存项
                    const firstKey = this._globalCache.keys().next().value;
                    this._globalCache.delete(firstKey);
                }

                this._globalCache.set(key, regex);
                return regex;
            },

            // 预编译常用正则表达式
            precompileCommonRegexes() {
                const commonPatterns = [
                    { key: 'verse_key', pattern: '^(.+) (\\d+):(\\d+)$', flags: '' },
                    { key: 'single_arabic_num', pattern: '^(\\d+)$', flags: '' },
                    { key: 'arabic_range', pattern: '^(\\d+)[~～˜∽-](\\d+)$', flags: '' },
                    { key: 'pure_digit', pattern: '^\\d+$', flags: '' },
                    { key: 'chapter_separator', pattern: '^(\\d+)[:：]?(.+)$', flags: '' }
                ];

                commonPatterns.forEach(({ key, pattern, flags }) => {
                    this.createOptimizedRegex(pattern, flags, key);
                });
            },

            // 获取缓存统计
            getCacheStats() {
                return {
                    size: this._globalCache.size,
                    hitCount: this._hitCount,
                    missCount: this._missCount,
                    hitRate: this._hitCount / (this._hitCount + this._missCount) * 100
                };
            },

            // 清除缓存
            clearCache() {
                this._globalCache.clear();
                this._hitCount = 0;
                this._missCount = 0;
            },

            // 优化字符串转义
            escapeRegexString(str) {
                // 缓存转义结果
                if (!this._escapeCache) {
                    this._escapeCache = new Map();
                }

                if (this._escapeCache.has(str)) {
                    return this._escapeCache.get(str);
                }

                const escaped = str.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
                this._escapeCache.set(str, escaped);
                return escaped;
            }
        };

        // --- 性能优化的引用解析模块（增强版） ---
        const ReferenceParser = {
            // 缓存预编译的正则表达式
            _cachedRegexes: {},
            _cachedBookParts: null,

            // 初始化和缓存正则表达式组件（优化版）
            _initializeRegexCache() {
                if (this._cachedBookParts) return; // 已初始化

                // 预编译书卷名称部分（使用优化的转义）
                this._cachedBookParts = {
                    bookAbbr: Object.keys(bookMap)
                        .sort((a, b) => b.length - a.length)
                        .map(name => RegexOptimizer.escapeRegexString(name))
                        .join('|'),
                    fullBookNames: Object.keys(fullBookNameToAbbrMap)
                        .sort((a, b) => b.length - a.length)
                        .map(name => RegexOptimizer.escapeRegexString(name))
                        .join('|')
                };

                // 预编译常用正则表达式（使用优化器）
                this._cachedRegexes = {
                    chineseNumber: RegexOptimizer.createOptimizedRegex(REGEX.CN_NUM, 'g', 'chinese_number'),
                    arabicNumber: RegexOptimizer.createOptimizedRegex(REGEX.AR_NUM_RANGE, 'g', 'arabic_number'),
                    verseKey: RegexOptimizer.createOptimizedRegex(REGEX.VERSE_KEY.source, 'g', 'verse_key'),
                    singleArabicNum: RegexOptimizer.createOptimizedRegex(REGEX.SINGLE_AR_NUM.source, 'g', 'single_arabic_num')
                };

                // 预编译常用正则表达式
                RegexOptimizer.precompileCommonRegexes();
            },

            // 构建引用匹配正则表达式（高度优化版）
            buildReferenceRegex() {
                this._initializeRegexCache();

                // 检查缓存
                const cacheKey = 'mainReferenceRegex';
                if (this._cachedRegexes[cacheKey]) {
                    return this._cachedRegexes[cacheKey];
                }

                const { bookAbbr, fullBookNames } = this._cachedBookParts;
                const cnNum = REGEX.CN_NUM;
                const cnVerseAndRange = REGEX.CN_VERSE_RANGE;
                const arNumAndRange = REGEX.AR_NUM_RANGE;

                // 构建优化的正则表达式模式
                const patterns = [
                    `(?:参)?(${fullBookNames}|${bookAbbr})(${cnNum})章(${cnVerseAndRange})(?:节)?(下)?`,
                    `(?:参)?(${bookAbbr})(${cnNum})(${arNumAndRange})(?:节)?(下)?`,
                    `(${cnNum})章(${cnVerseAndRange})(?:节)?(下)?`,
                    `(${cnNum})(${arNumAndRange})(?:节)?(下)?`,
                    `(?:参)?(${bookAbbr})(\\d+[:：]?${arNumAndRange})(?:节)?(下)?`,
                    `(?:参)?(${bookAbbr})\\s+(${arNumAndRange})(?:节)?(下)?`,
                    `(?:参)?(${bookAbbr})(${arNumAndRange})(?:节)?(下)?`,
                    `(${arNumAndRange})(?:节)?(下)?`
                ];

                // 使用优化器创建正则表达式
                const regex = RegexOptimizer.createOptimizedRegex(
                    patterns.join('|'),
                    'g',
                    cacheKey
                );

                // 缓存结果
                this._cachedRegexes[cacheKey] = regex;
                return regex;
            },

            // 构建行内上下文正则表达式（优化版）
            buildInlineContextRegex() {
                this._initializeRegexCache();

                const cacheKey = 'inlineContextRegex';
                if (this._cachedRegexes[cacheKey]) {
                    return this._cachedRegexes[cacheKey];
                }

                const { bookAbbr, fullBookNames } = this._cachedBookParts;
                const regex = new RegExp(
                    `(${fullBookNames}|${bookAbbr})` +
                    `([一二三四五六七八九十百千〇零]+)章`,
                    'g'
                );

                this._cachedRegexes[cacheKey] = regex;
                return regex;
            },

            // 获取缓存的正则表达式
            getCachedRegex(type) {
                this._initializeRegexCache();
                return this._cachedRegexes[type];
            },

            // 清除缓存（用于内存管理）
            clearCache() {
                this._cachedRegexes = {};
                this._cachedBookParts = null;
            }
        };

        // --- 正则表达式工厂模块 ---
        const RegexFactory = {
            // 创建书卷正则表达式部分
            createBookRegexPart(bookMap, escapeFunction = RegexOptimizer.escapeRegexString) {
                return Object.keys(bookMap)
                    .sort((a, b) => b.length - a.length)
                    .map(name => escapeFunction(name))
                    .join('|');
            },

            // 创建标准引用正则表达式
            createStandardReferenceRegex(bookAbbrPart) {
                const pattern = `^(${bookAbbrPart})\\s*(\\d+):(${REGEX.AR_NUM_RANGE})(下)?$`;
                return RegexOptimizer.createOptimizedRegex(pattern, 'gm', 'standard_reference');
            },

            // 创建单章书卷正则表达式
            createSingleChapterRegex(bookAbbrPart, withSpace = true) {
                const spacePattern = withSpace ? '\\s+' : '';
                const pattern = `^(${bookAbbrPart})${spacePattern}(${REGEX.AR_NUM_RANGE})(下)?$`;
                const cacheKey = `single_chapter_${withSpace ? 'with' : 'no'}_space`;
                return RegexOptimizer.createOptimizedRegex(pattern, 'gm', cacheKey);
            },

            // 创建复合引用正则表达式
            createCompoundReferenceRegex(bookAbbrPart, fullBookNamesPart) {
                const patterns = [
                    `(?:参)?(${fullBookNamesPart}|${bookAbbrPart})(${REGEX.CN_NUM})章(${REGEX.CN_VERSE_RANGE})(?:节)?(下)?`,
                    `(?:参)?(${bookAbbrPart})(${REGEX.CN_NUM})(${REGEX.AR_NUM_RANGE})(?:节)?(下)?`,
                    `(${REGEX.CN_NUM})章(${REGEX.CN_VERSE_RANGE})(?:节)?(下)?`,
                    `(${REGEX.CN_NUM})(${REGEX.AR_NUM_RANGE})(?:节)?(下)?`,
                    `(?:参)?(${bookAbbrPart})(\\d+[:：]?${REGEX.AR_NUM_RANGE})(?:节)?(下)?`,
                    `(?:参)?(${bookAbbrPart})\\s+(${REGEX.AR_NUM_RANGE})(?:节)?(下)?`,
                    `(?:参)?(${bookAbbrPart})(${REGEX.AR_NUM_RANGE})(?:节)?(下)?`,
                    // 新增：书名(全名)+中文节号（无章号，适用于单章书卷）- 如"犹大书一节"
                    `(?:参)?(${fullBookNamesPart})(${REGEX.CN_VERSE_RANGE})(?:节)?(下)?`,
                    // 🔧 新增：跨章引用格式 - 如"三章一节至四章三十一节"
                    `(${REGEX.CN_NUM})章(${REGEX.CN_VERSE_RANGE})(?:节)?(?:至|到|-|~)(${REGEX.CN_NUM})章(${REGEX.CN_VERSE_RANGE})(?:节)?(下)?`,
                    // 🔧 新增：仅中文节号范围（依赖上下文）- 如"十五至十七节"
                    `(${REGEX.CN_VERSE_RANGE})(?:节)?(下)?`,
                    `(${REGEX.AR_NUM_RANGE})(?:节)?(下)?`
                ];

                const finalRegex = RegexOptimizer.createOptimizedRegex(
                    patterns.join('|'),
                    'g',
                    'compound_reference'
                );

                // 🔧 调试：输出正则表达式信息
                console.log('🔍 正则表达式模式数量:', patterns.length);
                console.log('🔍 格式9模式:', patterns[8]);
                console.log('🔍 格式10模式:', patterns[9]);

                return finalRegex;
            },

            // 创建上下文正则表达式
            createContextRegex(bookAbbrPart, fullBookNamesPart) {
                const pattern = `(${fullBookNamesPart}|${bookAbbrPart})([一二三四五六七八九十百千〇零]+)章`;
                return RegexOptimizer.createOptimizedRegex(pattern, 'g', 'context_regex');
            },

            // 🔧 新增：创建单独书名上下文正则表达式 - 匹配"罗马书"这种没有章号的书名
            createBookOnlyContextRegex(bookAbbrPart, fullBookNamesPart) {
                // 只匹配完整书名（至少2个字符），避免单字简称的误匹配
                // 同时确保后面不是章号、节号、或"第X篇"格式
                const pattern = `(${fullBookNamesPart})(?![一二三四五六七八九十百千〇零]+[章节])(?![\\d]+[章节])(?!第[一二三四五六七八九十百千〇零]+篇)`;
                return RegexOptimizer.createOptimizedRegex(pattern, 'g', 'book_only_context');
            },

            // 获取常用正则表达式
            getCommonRegex(type) {
                const patterns = {
                    verse_key: REGEX.VERSE_KEY.source,
                    single_arabic: REGEX.SINGLE_AR_NUM.source,
                    arabic_range: REGEX.AR_NUM_RANGE_MATCH.source,
                    pure_digit: REGEX.PURE_DIGIT.source,
                    chapter_context: REGEX.CHAPTER_CONTEXT.source,
                    book_chapter_context: REGEX.BOOK_CHAPTER_CONTEXT.source
                };

                if (patterns[type]) {
                    return RegexOptimizer.createOptimizedRegex(patterns[type], '', type);
                }
                return null;
            },

            // 🚀 快速添加新正则表达式模式的接口
            addCustomPattern(name, pattern, flags = '', description = '') {
                console.log(`📝 添加新正则模式: ${name} - ${description}`);
                return RegexOptimizer.createOptimizedRegex(pattern, flags, `custom_${name}`);
            },

            // 🚀 创建自定义引用格式正则
            createCustomReferenceRegex(config) {
                /*
                使用示例:
                const customRegex = RegexFactory.createCustomReferenceRegex({
                    name: 'english_reference',
                    bookPattern: 'John|Matthew|Luke',
                    chapterPattern: '\\d+',
                    versePattern: '\\d+(?:-\\d+)?',
                    separator: ':',
                    flags: 'gi',
                    description: '英文书卷引用格式'
                });
                */
                const {
                    name,
                    bookPattern,
                    chapterPattern = '\\d+',
                    versePattern = '\\d+(?:[~～˜∽-]\\d+)?',
                    separator = ':',
                    flags = 'g',
                    description = ''
                } = config;

                if (!name || !bookPattern) {
                    throw new Error('name和bookPattern是必需的参数');
                }

                const pattern = `(${bookPattern})\\s*(${chapterPattern})${separator}(${versePattern})`;
                console.log(`📝 创建自定义引用正则: ${name} - ${description}`);
                console.log(`📋 模式: ${pattern}`);

                return RegexOptimizer.createOptimizedRegex(pattern, flags, `custom_ref_${name}`);
            },

            // 🚀 批量添加正则模式
            addMultiplePatterns(patterns) {
                /*
                使用示例:
                RegexFactory.addMultiplePatterns([
                    { name: 'email', pattern: '[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}', flags: 'gi' },
                    { name: 'phone', pattern: '\\d{3}-\\d{3}-\\d{4}', flags: 'g' },
                    { name: 'date', pattern: '\\d{4}-\\d{2}-\\d{2}', flags: 'g' }
                ]);
                */
                const results = {};
                patterns.forEach(({ name, pattern, flags = 'g', description = '' }) => {
                    try {
                        results[name] = this.addCustomPattern(name, pattern, flags, description);
                        console.log(`✅ 成功添加模式: ${name}`);
                    } catch (error) {
                        console.error(`❌ 添加模式失败: ${name} - ${error.message}`);
                        results[name] = null;
                    }
                });
                return results;
            },

            // 🚀 扩展现有引用格式
            extendReferenceFormats(newFormats) {
                /*
                使用示例:
                RegexFactory.extendReferenceFormats([
                    '(?:参)?(${bookAbbr})(\\d+)\\.(\\d+)', // 点号分隔格式
                    '(?:参)?(${bookAbbr})_(\\d+)_(\\d+)',   // 下划线分隔格式
                    '(${bookAbbr})\\s+第(\\d+)章第(\\d+)节' // 中文格式
                ]);
                */
                const bookAbbrPart = this.createBookRegexPart(window.bookMap || {});
                const fullBookNamesPart = this.createBookRegexPart(window.fullBookNameToAbbrMap || {});

                const expandedFormats = newFormats.map(format => {
                    return format
                        .replace(/\$\{bookAbbr\}/g, bookAbbrPart)
                        .replace(/\$\{fullBookNames\}/g, fullBookNamesPart)
                        .replace(/\$\{arNumRange\}/g, window.REGEX?.AR_NUM_RANGE || '\\d+(?:[~～˜∽-]\\d+)?');
                });

                const combinedPattern = expandedFormats.join('|');
                console.log(`📝 扩展引用格式，新增 ${newFormats.length} 种格式`);

                return RegexOptimizer.createOptimizedRegex(combinedPattern, 'g', 'extended_reference_formats');
            },

            // 解析单个引用匹配
            parseReferenceMatch(match, localBook, lastBook, localChapter, lastChapter) {
                let currentBook = null;
                let chapterNum = 0;
                let currentVerseStr = null;
                let currentSuffix = null;

                // 根据匹配到的不同正则组解析引用
                if (match[1] && match[2] && match[3]) { // 格式1: 书名+中文章+中文节
                    const bookNameOrAbbr = match[1].replace(/^参/, '');
                    currentBook = fullBookNameToAbbrMap[bookNameOrAbbr] || bookMap[bookNameOrAbbr] || bookNameOrAbbr;
                    if (!currentBook) return null;
                    chapterNum = chineseToArabic(match[2]);
                    currentVerseStr = parseChineseVerseRange(match[3]);
                    currentSuffix = match[4];
                    if (!currentVerseStr || chapterNum === 0) return null;
                } else if (match[5] && match[6] && match[7]) { // 格式2: 书名+中文章+阿拉伯节
                    let rawBook = match[5].replace(/^参/, '');
                    currentBook = bookMap[rawBook] || rawBook;
                    chapterNum = chineseToArabic(match[6]);
                    currentVerseStr = match[7];
                    currentSuffix = match[8];
                    if (!currentBook || chapterNum === 0) return null;
                } else if (match[9] && match[10]) { // 格式3: 中文章+中文节
                    const contextBook = localBook || lastBook;
                    if (!contextBook) return null;
                    currentBook = contextBook;
                    chapterNum = chineseToArabic(match[9]);
                    currentVerseStr = parseChineseVerseRange(match[10]);
                    currentSuffix = match[11];
                    if (!currentVerseStr || chapterNum === 0) return null;
                } else if (match[12] && match[13]) { // 格式4: 中文章+阿拉伯节
                    const contextBook = localBook || lastBook;
                    if (!contextBook) return null;
                    currentBook = contextBook;
                    chapterNum = chineseToArabic(match[12]);
                    currentVerseStr = match[13];
                    currentSuffix = match[14];
                    if (chapterNum === 0) return null;
                } else {
                    // 处理其他格式...
                    return this.parseOtherFormats(match, localBook, lastBook, localChapter, lastChapter);
                }

                return {
                    book: currentBook,
                    chapter: chapterNum,
                    verse: currentVerseStr,
                    suffix: currentSuffix,
                    originalText: match[0],
                    index: match.index
                };
            },

            // 处理其他引用格式
            parseOtherFormats(match, localBook, lastBook, localChapter, lastChapter) {
                // 这里处理格式5-8的解析逻辑
                // 为了保持代码简洁，这里只展示结构
                // 实际实现需要包含原来的所有格式解析逻辑
                return null; // 临时返回，需要完整实现
            }
        };

        // --- 性能优化工具模块 ---
        const PerformanceUtils = {
            // 高性能HTML构建器
            createHtmlBuilder() {
                const parts = [];
                return {
                    add(html) { parts.push(html); },
                    addMultiple(htmlArray) { parts.push(...htmlArray); },
                    toString() { return parts.join(''); },
                    clear() { parts.length = 0; }
                };
            },

            // 批量DOM操作优化
            batchDomUpdate(element, updateFn) {
                const originalDisplay = element.style.display;
                element.style.display = 'none'; // 隐藏元素减少重排
                updateFn();
                element.style.display = originalDisplay;
            },

            // 防抖函数
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        };

        // --- 性能监控模块（增强版） ---
        const PerformanceMonitor = {
            metrics: {},
            memoryBaseline: null,

            // 开始计时
            startTimer(name) {
                this.metrics[name] = {
                    start: performance.now(),
                    memoryStart: this.getCurrentMemoryUsage()
                };
            },

            // 结束计时
            endTimer(name) {
                if (this.metrics[name]) {
                    this.metrics[name].duration = performance.now() - this.metrics[name].start;
                    this.metrics[name].memoryEnd = this.getCurrentMemoryUsage();
                    this.metrics[name].memoryDelta = this.metrics[name].memoryEnd - this.metrics[name].memoryStart;
                    return this.metrics[name].duration;
                }
                return 0;
            },

            // 获取当前内存使用情况
            getCurrentMemoryUsage() {
                if (performance.memory) {
                    return performance.memory.usedJSHeapSize / 1024 / 1024; // MB
                }
                return 0;
            },

            // 获取性能报告
            getReport() {
                const report = {};
                for (const [name, metric] of Object.entries(this.metrics)) {
                    if (metric.duration !== undefined) {
                        report[name] = {
                            duration: `${metric.duration.toFixed(2)}ms`,
                            memoryDelta: `${metric.memoryDelta?.toFixed(2) || 0}MB`
                        };
                    }
                }
                return report;
            },

            // 清除指标
            clear() {
                this.metrics = {};
            },

            // 设置内存基线
            setMemoryBaseline() {
                this.memoryBaseline = this.getCurrentMemoryUsage();
            },

            // 检查内存压力
            checkMemoryPressure() {
                const current = this.getCurrentMemoryUsage();
                const threshold = 100; // 100MB阈值
                return current > threshold;
            }
        };

        // --- 大文本处理优化模块 ---
        const TextProcessingOptimizer = {
            // 配置参数
            config: {
                chunkSize: 1000,        // 每块行数
                maxWorkers: 2,          // 最大Worker数量
                enableWorker: true,     // 是否启用Web Worker
                progressCallback: null, // 进度回调
                memoryThreshold: 50     // 内存阈值(MB)
            },

            // Worker池
            workers: [],
            workerBusy: [],
            currentProcessing: null,

            // 初始化Worker池
            async initWorkers() {
                if (!this.config.enableWorker || !window.Worker) {
                    console.log('Web Worker不可用，使用主线程处理');
                    return false;
                }

                try {
                    for (let i = 0; i < this.config.maxWorkers; i++) {
                        const worker = new Worker('text-processor-worker.js');
                        this.workers.push(worker);
                        this.workerBusy.push(false);

                        // 初始化Worker数据
                        await this.initWorkerData(worker);
                    }
                    console.log(`已初始化 ${this.workers.length} 个Worker`);
                    return true;
                } catch (error) {
                    console.error('Worker初始化失败:', error);
                    return false;
                }
            },

            // 初始化Worker数据
            initWorkerData(worker) {
                return new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('Worker初始化超时'));
                    }, 5000);

                    worker.onmessage = (e) => {
                        if (e.data.type === 'init-complete') {
                            clearTimeout(timeout);
                            resolve();
                        }
                    };

                    worker.onerror = (error) => {
                        clearTimeout(timeout);
                        reject(error);
                    };

                    worker.postMessage({
                        type: 'init',
                        data: {
                            bibleData: DataManager.getBibleData(),
                            bookMap: window.bookMap,
                            fullBookNameToAbbrMap: window.fullBookNameToAbbrMap,
                            bookChapterLimits: window.bookChapterLimits,
                            singleChapterBooks: window.singleChapterBooks
                        }
                    });
                });
            },

            // 获取可用的Worker
            getAvailableWorker() {
                for (let i = 0; i < this.workers.length; i++) {
                    if (!this.workerBusy[i]) {
                        return { worker: this.workers[i], index: i };
                    }
                }
                return null;
            },

            // 分块处理大文本
            async processLargeText(text, options = {}) {
                const startTime = performance.now();
                PerformanceMonitor.startTimer('processLargeText');

                try {
                    // 检查文本大小
                    const textSize = text.length;
                    const estimatedLines = text.split('\n').length;

                    console.log(`开始处理大文本: ${textSize} 字符, ${estimatedLines} 行`);

                    // 如果文本较小，直接使用原有方法
                    if (estimatedLines < this.config.chunkSize || !this.workers.length) {
                        return this.processSingleThread(text, options);
                    }

                    // 分块处理
                    const chunks = this.splitTextIntoChunks(text, this.config.chunkSize);
                    const results = await this.processChunksParallel(chunks, options);

                    // 合并结果
                    const finalHtml = results.map(r => r.html).join('');

                    const duration = PerformanceMonitor.endTimer('processLargeText');
                    console.log(`大文本处理完成: ${duration.toFixed(2)}ms`);

                    return finalHtml;

                } catch (error) {
                    console.error('大文本处理失败:', error);
                    // 降级到单线程处理
                    return this.processSingleThread(text, options);
                }
            },

            // 将文本分割为块
            splitTextIntoChunks(text, chunkSize) {
                const lines = text.split('\n');
                const chunks = [];

                for (let i = 0; i < lines.length; i += chunkSize) {
                    const chunk = lines.slice(i, i + chunkSize).join('\n');
                    chunks.push(chunk);
                }

                return chunks;
            },

            // 并行处理文本块
            async processChunksParallel(chunks, options) {
                const results = new Array(chunks.length);
                const promises = [];

                for (let i = 0; i < chunks.length; i++) {
                    const promise = this.processChunkWithWorker(chunks[i], i, chunks.length, options)
                        .then(result => {
                            results[i] = result;

                            // 更新进度
                            if (this.config.progressCallback) {
                                const progress = ((i + 1) / chunks.length) * 100;
                                this.config.progressCallback(progress, i + 1, chunks.length);
                            }
                        });

                    promises.push(promise);
                }

                await Promise.all(promises);
                return results;
            },

            // 使用Worker处理单个文本块
            processChunkWithWorker(chunk, chunkIndex, totalChunks, options) {
                return new Promise((resolve, reject) => {
                    const workerInfo = this.getAvailableWorker();

                    if (!workerInfo) {
                        // 如果没有可用Worker，使用主线程处理
                        resolve(this.processChunkMainThread(chunk, chunkIndex, options));
                        return;
                    }

                    const { worker, index } = workerInfo;
                    this.workerBusy[index] = true;

                    const timeout = setTimeout(() => {
                        this.workerBusy[index] = false;
                        reject(new Error('Worker处理超时'));
                    }, 30000); // 30秒超时

                    worker.onmessage = (e) => {
                        if (e.data.type === 'chunk-processed') {
                            clearTimeout(timeout);
                            this.workerBusy[index] = false;
                            resolve(e.data.data);
                        }
                    };

                    worker.onerror = (error) => {
                        clearTimeout(timeout);
                        this.workerBusy[index] = false;
                        reject(error);
                    };

                    worker.postMessage({
                        type: 'process-chunk',
                        data: {
                            chunk,
                            chunkIndex,
                            totalChunks,
                            options
                        }
                    });
                });
            },

            // 主线程处理文本块（降级方案）
            processChunkMainThread(chunk, chunkIndex, options) {
                try {
                    // 简化的主线程处理逻辑
                    const lines = chunk.split('\n');
                    let html = '';

                    lines.forEach(line => {
                        html += `<div class="line-group">`;
                        html += `<div class="original-line-wrapper">${escapeHtml(line)}</div>`;
                        html += `</div>`;
                    });

                    return {
                        chunkIndex,
                        html,
                        processed: true,
                        error: null
                    };
                } catch (error) {
                    return {
                        chunkIndex,
                        html: `<div class="line-group"><div class="scripture-not-found">处理块 ${chunkIndex + 1} 时出错: ${escapeHtml(error.message)}</div></div>`,
                        processed: false,
                        error: error.message
                    };
                }
            },

            // 单线程处理（原有方法的包装）
            processSingleThread(text, options) {
                return processText(text, options.withVerses);
            },

            // 清理Worker
            cleanup() {
                this.workers.forEach(worker => {
                    worker.terminate();
                });
                this.workers = [];
                this.workerBusy = [];
            },

            // 设置进度回调
            setProgressCallback(callback) {
                this.config.progressCallback = callback;
            }
        };

        // --- 进度管理器 ---
        const ProgressManager = {
            // DOM元素
            elements: {
                container: null,
                bar: null,
                current: null,
                total: null,
                memory: null,
                time: null,
                percent: null,
                cancel: null
            },

            // 状态
            isVisible: false,
            startTime: 0,
            cancelled: false,
            onCancel: null,

            // 初始化
            init() {
                this.elements.container = document.getElementById('progressContainer');
                this.elements.bar = document.getElementById('progressBar');
                this.elements.current = document.getElementById('progressCurrent');
                this.elements.total = document.getElementById('progressTotal');
                this.elements.memory = document.getElementById('progressMemory');
                this.elements.time = document.getElementById('progressTime');
                this.elements.percent = document.getElementById('progressPercent');
                this.elements.cancel = document.getElementById('progressCancel');

                // 绑定取消按钮事件
                if (this.elements.cancel) {
                    this.elements.cancel.addEventListener('click', () => {
                        this.cancel();
                    });
                }
            },

            // 显示进度条
            show(title = '正在处理...', total = 0) {
                if (!this.elements.container) this.init();

                this.isVisible = true;
                this.cancelled = false;
                this.startTime = performance.now();

                // 更新标题
                const titleElement = this.elements.container.querySelector('.progress-title');
                if (titleElement) titleElement.textContent = title;

                // 初始化数值
                this.elements.total.textContent = total;
                this.elements.current.textContent = '0';
                this.elements.percent.textContent = '0%';
                this.elements.bar.style.width = '0%';

                // 显示容器
                this.elements.container.classList.add('show');
            },

            // 更新进度
            update(current, total, extraInfo = {}) {
                if (!this.isVisible || this.cancelled) return;

                const percent = total > 0 ? (current / total) * 100 : 0;
                const elapsed = performance.now() - this.startTime;

                // 更新进度条
                this.elements.bar.style.width = `${percent}%`;
                this.elements.current.textContent = current;
                this.elements.total.textContent = total;
                this.elements.percent.textContent = `${Math.round(percent)}%`;
                this.elements.time.textContent = Math.round(elapsed);

                // 更新内存使用
                const memoryUsage = PerformanceMonitor.getCurrentMemoryUsage();
                this.elements.memory.textContent = memoryUsage.toFixed(1);

                // 更新性能面板
                PerformancePanel.update({
                    speed: extraInfo.speed || 0,
                    memory: memoryUsage,
                    workers: extraInfo.workers || '0/0',
                    cache: extraInfo.cacheHitRate || 0
                });
            },

            // 隐藏进度条
            hide() {
                if (!this.elements.container) return;

                this.isVisible = false;
                this.elements.container.classList.remove('show');
                PerformancePanel.hide();
            },

            // 取消操作
            cancel() {
                this.cancelled = true;
                if (this.onCancel) {
                    this.onCancel();
                }
                this.hide();
            },

            // 设置取消回调
            setCancelCallback(callback) {
                this.onCancel = callback;
            },

            // 检查是否已取消
            isCancelled() {
                return this.cancelled;
            }
        };

        // --- 性能监控面板 ---
        const PerformancePanel = {
            elements: {
                panel: null,
                speed: null,
                memory: null,
                workers: null,
                cache: null
            },

            isVisible: false,

            // 初始化
            init() {
                this.elements.panel = document.getElementById('performancePanel');
                this.elements.speed = document.getElementById('perfSpeed');
                this.elements.memory = document.getElementById('perfMemory');
                this.elements.workers = document.getElementById('perfWorkers');
                this.elements.cache = document.getElementById('perfCache');
            },

            // 显示面板
            show() {
                if (!this.elements.panel) this.init();
                this.isVisible = true;
                this.elements.panel.classList.add('show');
            },

            // 隐藏面板
            hide() {
                if (!this.elements.panel) return;
                this.isVisible = false;
                this.elements.panel.classList.remove('show');
            },

            // 更新数据
            update(data) {
                if (!this.isVisible) this.show();

                if (this.elements.speed) this.elements.speed.textContent = `${data.speed} 行/秒`;
                if (this.elements.memory) this.elements.memory.textContent = `${data.memory.toFixed(1)} MB`;
                if (this.elements.workers) this.elements.workers.textContent = data.workers;
                if (this.elements.cache) this.elements.cache.textContent = `${data.cache.toFixed(1)}%`;
            }
        };

        // --- 大文本检测器 ---
        const LargeTextDetector = {
            // 配置
            config: {
                lineThreshold: 500,     // 行数阈值
                sizeThreshold: 50000,   // 字符数阈值
                showWarning: true       // 是否显示警告
            },

            // DOM元素
            elements: {
                warning: null,
                enableBtn: null,
                continueBtn: null
            },

            // 初始化
            init() {
                this.elements.warning = document.getElementById('largeTextWarning');
                this.elements.enableBtn = document.getElementById('enableOptimizedProcessing');
                this.elements.continueBtn = document.getElementById('continueNormalProcessing');

                // 绑定事件
                if (this.elements.enableBtn) {
                    this.elements.enableBtn.addEventListener('click', () => {
                        this.enableOptimizedProcessing();
                    });
                }

                if (this.elements.continueBtn) {
                    this.elements.continueBtn.addEventListener('click', () => {
                        this.continueNormalProcessing();
                    });
                }
            },

            // 检测大文本
            detect(text) {
                const lines = text.split('\n').length;
                const size = text.length;

                return {
                    isLarge: lines > this.config.lineThreshold || size > this.config.sizeThreshold,
                    lines,
                    size,
                    estimatedProcessingTime: this.estimateProcessingTime(lines, size)
                };
            },

            // 估算处理时间
            estimateProcessingTime(lines, size) {
                // 基于经验的估算公式
                const baseTime = 0.1; // 每行基础时间(ms)
                const sizeMultiplier = size / 10000; // 大小影响因子
                return Math.round(lines * baseTime * sizeMultiplier);
            },

            // 显示警告
            showWarning(detection) {
                if (!this.config.showWarning || !this.elements.warning) return false;

                // 更新警告信息
                const message = `检测到 ${detection.lines} 行文本 (${(detection.size / 1024).toFixed(1)} KB)，预计处理时间 ${detection.estimatedProcessingTime} ms。`;
                const messageElement = this.elements.warning.querySelector('p');
                if (messageElement) {
                    messageElement.textContent = message;
                }

                this.elements.warning.classList.add('show');
                return true;
            },

            // 隐藏警告
            hideWarning() {
                if (this.elements.warning) {
                    this.elements.warning.classList.remove('show');
                }
            },

            // 启用优化处理
            enableOptimizedProcessing() {
                this.hideWarning();
                TextProcessingOptimizer.config.enableWorker = true;
                console.log('已启用优化处理模式');
            },

            // 继续普通处理
            continueNormalProcessing() {
                this.hideWarning();
                TextProcessingOptimizer.config.enableWorker = false;
                console.log('继续使用普通处理模式');
            }
        };

        // --- 性能优化的经文查找模块 ---
        const VerseSearcher = {
            // 缓存查找结果
            _verseCache: new Map(),
            _cacheMaxSize: 1000,

            // 查找单个经文（带缓存）
            findVerse(book, chapter, verse, bibleData) {
                const key = `${book} ${chapter}:${verse}`;

                // 检查缓存
                if (this._verseCache.has(key)) {
                    return this._verseCache.get(key);
                }

                const result = bibleData[key] || null;

                // 🔧 调试：如果找不到，显示相似的键
                if (!result && bibleData) {
                    const similarKeys = Object.keys(bibleData).filter(k =>
                        k.includes(book) && k.includes(`${chapter}:`)
                    ).slice(0, 3);
                    console.log(`❌ 未找到 "${key}"，相似的键:`, similarKeys);
                }

                // 缓存结果
                if (this._verseCache.size >= this._cacheMaxSize) {
                    // 清除最旧的缓存项
                    const firstKey = this._verseCache.keys().next().value;
                    this._verseCache.delete(firstKey);
                }
                this._verseCache.set(key, result);

                return result;
            },

            // 查找经文范围（优化版）
            findVerseRange(book, chapter, startVerse, endVerse, bibleData) {
                const verses = [];
                const batchKeys = [];

                // 批量构建键名
                for (let v = startVerse; v <= endVerse; v++) {
                    batchKeys.push(`${book} ${chapter}:${v}`);
                }

                // 批量查找
                batchKeys.forEach((key, index) => {
                    const verse = this.findVerse(book, chapter, startVerse + index, bibleData);
                    if (verse) {
                        verses.push({ verse: startVerse + index, text: verse });
                    }
                });

                return verses;
            },

            // 解析节号范围（使用缓存的正则）
            parseVerseRange(verseStr) {
                const singleRegex = ReferenceParser.getCachedRegex('singleArabicNum');
                if (singleRegex && singleRegex.test(verseStr)) {
                    return { start: parseInt(verseStr, 10), end: parseInt(verseStr, 10) };
                }

                const rangeMatch = verseStr.match(REGEX.AR_NUM_RANGE_MATCH);
                if (rangeMatch) {
                    return {
                        start: parseInt(rangeMatch[1], 10),
                        end: parseInt(rangeMatch[2], 10)
                    };
                }

                return null;
            },

            // 格式化经文输出（使用DocumentFragment优化DOM操作）
            formatVerseOutput(book, chapter, verseRange, verses, suffix) {
                if (!verses || verses.length === 0) {
                    return `<div class="scripture-not-found">未找到经文: ${book} ${chapter}:${verseRange.start}${verseRange.end !== verseRange.start ? '-' + verseRange.end : ''}${suffix || ''}</div>`;
                }

                // 使用DocumentFragment优化DOM操作
                const fragment = document.createDocumentFragment();
                verses.forEach(v => {
                    const div = document.createElement('div');
                    div.className = 'scripture-text';

                    const verseSpan = document.createElement('span');
                    verseSpan.className = 'verse-num';
                    verseSpan.textContent = v.verse;

                    div.appendChild(verseSpan);
                    div.appendChild(document.createTextNode(' ' + v.text));
                    fragment.appendChild(div);
                });

                // 转换为HTML字符串
                const tempDiv = document.createElement('div');
                tempDiv.appendChild(fragment);
                return tempDiv.innerHTML;
            },

            // 清除缓存
            clearCache() {
                this._verseCache.clear();
            },

            // 新增：回溯查找经文功能
            findVerseWithBacktrack(book, chapter, verse, bibleData, previousMatches = []) {
                const originalKey = `${book} ${chapter}:${verse}`;

                // 首先尝试直接查找
                let result = this.findVerse(book, chapter, verse, bibleData);
                if (result) {
                    return { text: result, key: originalKey, backtracked: false };
                }

                // 开始回溯查找
                console.log(`🔍 开始回溯查找: ${originalKey}`);

                // 1. 首先尝试在当前书卷的前面章节中查找
                if (chapter > 1) {
                    for (let c = chapter - 1; c >= 1; c--) {
                        const backtrackKey = `${book} ${c}:${verse}`;
                        const backtrackResult = this.findVerse(book, c, verse, bibleData);
                        if (backtrackResult) {
                            console.log(`✅ 在 ${book} ${c}章 找到匹配的第${verse}节`);
                            return {
                                text: backtrackResult,
                                key: backtrackKey,
                                backtracked: true,
                                originalKey: originalKey,
                                backtrackInfo: `从 ${originalKey} 回溯到 ${backtrackKey}`
                            };
                        }
                    }
                }

                // 2. 如果在当前书卷中没找到，尝试在之前匹配的书卷中查找
                if (previousMatches && previousMatches.length > 0) {
                    // 从最近的匹配开始向前查找
                    for (let i = previousMatches.length - 1; i >= 0; i--) {
                        const prevMatch = previousMatches[i];

                        // 在该书卷的该章节查找
                        let searchKey = `${prevMatch.book} ${prevMatch.chapter}:${verse}`;
                        let searchResult = this.findVerse(prevMatch.book, prevMatch.chapter, verse, bibleData);

                        if (searchResult) {
                            console.log(`✅ 在之前的匹配 ${searchKey} 中找到`);
                            return {
                                text: searchResult,
                                key: searchKey,
                                backtracked: true,
                                originalKey: originalKey,
                                backtrackInfo: `从 ${originalKey} 回溯到 ${searchKey}`
                            };
                        }

                        // 如果该章节没有，尝试该书卷的前面章节
                        if (prevMatch.chapter > 1) {
                            for (let c = prevMatch.chapter - 1; c >= 1; c--) {
                                searchKey = `${prevMatch.book} ${c}:${verse}`;
                                searchResult = this.findVerse(prevMatch.book, c, verse, bibleData);
                                if (searchResult) {
                                    console.log(`✅ 在之前书卷的前面章节 ${searchKey} 中找到`);
                                    return {
                                        text: searchResult,
                                        key: searchKey,
                                        backtracked: true,
                                        originalKey: originalKey,
                                        backtrackInfo: `从 ${originalKey} 回溯到 ${searchKey}`
                                    };
                                }
                            }
                        }
                    }
                }

                // 3. 如果都没找到，返回null
                console.log(`❌ 回溯查找失败: ${originalKey}`);
                return {
                    text: null,
                    key: originalKey,
                    backtracked: false,
                    backtrackInfo: `无法找到 ${originalKey}，已尝试回溯查找`
                };
            }
        };

        // --- 文本处理主函数（高性能版 - 支持大文本优化） ---
        async function processText(text, withVerses = true, forceOptimized = false) {
            return ErrorHandler.safeExecute(async () => {
                // 参数验证
                if (!TypeValidator.isValidString(text, true)) {
                    return `<div class="line-group"><div class="scripture-not-found">错误：输入文本为空或无效。</div></div>`;
                }

                if (typeof withVerses !== 'boolean') {
                    withVerses = true; // 默认值
                }

                PerformanceMonitor.startTimer('processText');

                // 如果需要查找经文但 JSON 数据未加载
                if (withVerses && !DataManager.isDataLoaded()) {
                    return `<div class="line-group"><div class="scripture-not-found">错误：请先成功加载经文 JSON 文件。</div></div>`;
                }

                // 大文本检测
                const detection = LargeTextDetector.detect(text);

                // 如果是大文本且启用了优化处理
                if ((detection.isLarge || forceOptimized) && TextProcessingOptimizer.config.enableWorker) {
                    console.log('使用优化处理模式处理大文本');

                    // 初始化Worker（如果尚未初始化）
                    if (TextProcessingOptimizer.workers.length === 0) {
                        const workerInitialized = await TextProcessingOptimizer.initWorkers();
                        if (!workerInitialized) {
                            console.log('Worker初始化失败，降级到普通处理');
                            return processTextLegacy(text, withVerses);
                        }
                    }

                    // 设置进度回调
                    TextProcessingOptimizer.setProgressCallback((progress, current, total) => {
                        ProgressManager.update(current, total, {
                            speed: Math.round((current * 1000) / (performance.now() - ProgressManager.startTime)),
                            workers: `${TextProcessingOptimizer.workers.filter((_, i) => TextProcessingOptimizer.workerBusy[i]).length}/${TextProcessingOptimizer.workers.length}`,
                            cacheHitRate: RegexOptimizer ? RegexOptimizer.getCacheStats().hitRate : 0
                        });
                    });

                    // 显示进度条
                    ProgressManager.show('正在处理大文本...', Math.ceil(detection.lines / TextProcessingOptimizer.config.chunkSize));

                    // 设置取消回调
                    ProgressManager.setCancelCallback(() => {
                        console.log('用户取消了文本处理');
                        TextProcessingOptimizer.cleanup();
                    });

                    try {
                        // 使用优化处理
                        const result = await TextProcessingOptimizer.processLargeText(text, { withVerses });

                        ProgressManager.hide();

                        const duration = PerformanceMonitor.endTimer('processText');
                        console.log(`优化处理完成: ${duration.toFixed(2)}ms`);

                        return result;

                    } catch (error) {
                        ProgressManager.hide();
                        console.error('优化处理失败，降级到普通处理:', error);
                        return processTextLegacy(text, withVerses);
                    }
                } else {
                    // 使用传统处理方式
                    return processTextLegacy(text, withVerses);
                }
            }, '文本处理', '<div class="line-group"><div class="scripture-not-found">文本处理出错，请重试。</div></div>');
        }

        // --- 传统文本处理函数（保持原有逻辑） ---
        function processTextLegacy(text, withVerses = true) {

            const lines = text.split('\n');
            const htmlBuilder = PerformanceUtils.createHtmlBuilder(); // 使用高性能HTML构建器
            const bibleData = DataManager.getBibleData();

            // 使用优化的正则表达式工厂创建书卷正则部分
            const bookAbbrRegexPart = RegexFactory.createBookRegexPart(bookMap);
            const fullBookNamesRegexPart = RegexFactory.createBookRegexPart(fullBookNameToAbbrMap);

            // 定义中文数字和阿拉伯数字的正则模式
            const cnNum = REGEX.CN_NUM; // 使用预定义的中文数字正则
            const cnVerseAndRange = REGEX.CN_VERSE_RANGE; // 使用预定义的中文节号或范围
            const arNumAndRange = REGEX.AR_NUM_RANGE; // 使用预定义的阿拉伯数字节号或范围

            // 使用优化的正则表达式工厂创建复杂正则
            const refRegex = RegexFactory.createCompoundReferenceRegex(bookAbbrRegexPart, fullBookNamesRegexPart);
            const inlineContextRegex = RegexFactory.createContextRegex(bookAbbrRegexPart, fullBookNamesRegexPart);

            // lastBook 和 lastChapter 在每次调用 processText 时重新初始化，以确保上下文独立
            let lastBook = null; // 上一个成功解析的书卷 (用于跨行或行内较早的引用)
            let lastChapter = null; // 上一个成功解析的章号 (用于跨行或行内较早的引用)

            // 新增：用于回溯查找的历史匹配记录和段落上下文
            let previousMatches = []; // 存储之前成功匹配的书卷和章节信息
            let paragraphContexts = []; // 存储每个段落的上下文信息，用于回溯

            for (const line of lines) { // 逐行处理
                htmlBuilder.add(`<div class="line-group">`); // 每行原文及其处理结果构成一个组
                htmlBuilder.add(`<div class="original-line-wrapper">${escapeHtml(line)}</div>`); // 显示原始行

                let localBook = null; // 当前行内已解析的书卷上下文
                let localChapter = null; // 当前行内已解析的章号上下文
                inlineContextRegex.lastIndex = 0; // 重置行内上下文正则的匹配位置
                let contextMatch;

                // 尝试从当前行提取书卷和章的上下文 (例如："约翰福音三章...")
                console.log(`🔍 开始检查行内上下文: "${line}"`);
                console.log(`🔍 上下文正则表达式: ${inlineContextRegex.source}`);
                while ((contextMatch = inlineContextRegex.exec(line)) !== null) {
                    console.log(`🔍 上下文匹配: "${contextMatch[0]}", 书名组: "${contextMatch[1]}", 章号组: "${contextMatch[2]}"`);
                    const bookNameOrAbbrCtx = contextMatch[1]; // 书名或简称
                    const chapterStrCtx = contextMatch[2]; // 中文章号字符串
                    // 将书名转换为标准简称
                    const bookCandidateCtx = fullBookNameToAbbrMap[bookNameOrAbbrCtx] || bookMap[bookNameOrAbbrCtx] || bookNameOrAbbrCtx;

                    if (bookCandidateCtx) { // 如果是有效的书名
                        const chapterNumCtx = chineseToArabic(chapterStrCtx); // 中文章号转为阿拉伯数字
                        if (chapterNumCtx > 0) { // 如果是有效的章号
                            localBook = bookCandidateCtx;    // 设置行内书卷上下文
                            localChapter = chapterNumCtx;  // 设置行内章号上下文
                            // 更新全局（函数级）上下文，供后续无明确书卷/章号的引用使用
                            lastBook = localBook;
                            lastChapter = localChapter;
                            console.log(`📖 上下文设置: localBook="${localBook}", localChapter=${localChapter}, lastBook="${lastBook}", lastChapter=${lastChapter}`);

                            // 🔧 修复：将上下文设置也添加到历史匹配记录中，确保回溯功能能使用最新的上下文
                            previousMatches.push({
                                book: bookCandidateCtx,
                                chapter: chapterNumCtx,
                                verse: 1, // 上下文设置时使用默认节号1
                                isContext: true // 标记这是上下文设置，不是具体的经文引用
                            });
                            console.log(`🔄 上下文添加到历史匹配: ${bookCandidateCtx} ${chapterNumCtx}章`);

                            // 只保留最近的10个匹配记录，避免内存过度使用
                            if (previousMatches.length > 10) {
                                previousMatches.splice(0, previousMatches.length - 10);
                            }
                        }
                    }
                }

                // 🔧 新增：检查单独的书名（没有章号）作为上下文 - 如"罗马书"
                const bookOnlyRegex = RegexFactory.createBookOnlyContextRegex(bookAbbrRegexPart, fullBookNamesRegexPart);
                console.log(`🔍 检查单独书名上下文: ${bookOnlyRegex.source}`);
                let bookOnlyMatch;
                while ((bookOnlyMatch = bookOnlyRegex.exec(line)) !== null) {
                    console.log(`🔍 单独书名匹配: "${bookOnlyMatch[0]}", 书名组: "${bookOnlyMatch[1]}"`);
                    const bookNameOrAbbrOnly = bookOnlyMatch[1];
                    const bookCandidateOnly = fullBookNameToAbbrMap[bookNameOrAbbrOnly] || bookMap[bookNameOrAbbrOnly] || bookNameOrAbbrOnly;

                    if (bookCandidateOnly) {
                        // 设置为该书的第1章上下文
                        localBook = bookCandidateOnly;
                        localChapter = 1;
                        lastBook = localBook;
                        lastChapter = localChapter;
                        console.log(`📖 单独书名上下文设置: localBook="${localBook}", localChapter=${localChapter} (默认第1章)`);

                        // 添加到历史匹配记录
                        previousMatches.push({
                            book: bookCandidateOnly,
                            chapter: 1,
                            verse: 1,
                            isContext: true,
                            isBookOnly: true // 标记这是单独书名上下文
                        });
                        console.log(`🔄 单独书名添加到历史匹配: ${bookCandidateOnly} 1章 (默认)`);

                        // 只保留最近的10个匹配记录
                        if (previousMatches.length > 10) {
                            previousMatches.splice(0, previousMatches.length - 10);
                        }
                    }
                }

                let match; // 用于存储 refRegex 的匹配结果
                const foundItems = []; // 存储当前行找到的所有经文引用对象
                refRegex.lastIndex = 0; // 重置主要引用正则的匹配位置

                // 在当前行循环查找所有经文引用
                while ((match = refRegex.exec(line)) !== null) {
                    // 🔧 调试：显示匹配的详细信息
                    console.log('🔍 匹配详情:', {
                        匹配文本: match[0],
                        匹配位置: match.index,
                        总组数: match.length,
                        非空组: match.map((group, index) => group ? `[${index}]: "${group}"` : null).filter(Boolean),
                        所有组: match.slice(0, 40).map((group, index) => `[${index}]: ${group || 'null'}`).join(', ')
                    });
                    let currentBook = null;     // 当前匹配到的书卷
                    let chapterNum = 0;       // 当前匹配到的章号
                    let currentVerseStr = null; // 当前匹配到的节号字符串 (可能是单个数字或范围)
                    let currentSuffix = null;   // 当前匹配到的后缀 (例如 "下")

                    // 回溯相关变量（所有格式都需要）
                    let isBacktracked = false;
                    let backtrackInfo = null;

                    // 根据匹配到的不同正则组解析引用
                    // 🔧 调试：检查各个格式的匹配情况
                    console.log('🔍 格式检查:', {
                        格式1: !!(match[1] && match[2] && match[3]),
                        格式2: !!(match[6] && match[7] && match[8]),
                        跨章格式: !!(match[26] && match[27] && match[28] && match[29]),
                        格式9: !!match[32],
                        格式10: !!match[34]
                    });

                    if (match[1] && match[2] && match[3]) { // 格式1 匹配成功: 书名(全/简)+中文章+中文节
                        console.log(`🔍 格式1匹配: "${match[0]}", 书名组: "${match[1]}", 章号组: "${match[2]}", 节号组: "${match[3]}", 后缀组: "${match[4]}"`);
                        const bookNameOrAbbr = match[1].replace(/^参/, ''); // 移除可选的 "参" 前缀
                        currentBook = fullBookNameToAbbrMap[bookNameOrAbbr] || bookMap[bookNameOrAbbr] || bookNameOrAbbr;
                        if (!currentBook) {
                            console.log(`❌ 格式1无效书名: "${bookNameOrAbbr}"`);
                            continue; // 无效书名，跳过
                        }
                        chapterNum = chineseToArabic(match[2]); // 中文章号转数字
                        currentVerseStr = parseChineseVerseRange(match[3]); // 解析中文节号或范围
                        currentSuffix = match[4]; // 获取后缀 "下"
                        console.log(`📖 格式1解析结果: 书名="${currentBook}", 章号=${chapterNum}, 节号="${currentVerseStr}", 后缀="${currentSuffix}"`);
                        if (!currentVerseStr || chapterNum === 0) {
                            console.log(`❌ 格式1验证失败: 节号="${currentVerseStr}", 章号=${chapterNum}`);
                            continue; // 无效章或节，跳过
                        }

                        // 更新上下文
                        lastBook = currentBook; lastChapter = chapterNum;
                        localBook = currentBook; localChapter = chapterNum;

                        // 🔧 新增：将格式1的结果添加到foundItems
                        if (currentVerseStr) {
                            // 处理节号范围（如"一至三"）
                            const verseRange = VerseSearcher.parseVerseRange(currentVerseStr);
                            if (verseRange) {
                                for (let v = verseRange.start; v <= verseRange.end; v++) {
                                    // 立即查找经文文本
                                    let verseText = null;
                                    let backtrackInfo = null;

                                    if (withVerses && bibleData) {
                                        const searchResult = VerseSearcher.findVerseWithBacktrack(
                                            currentBook, chapterNum, v, bibleData, previousMatches
                                        );
                                        verseText = searchResult.text;
                                        if (searchResult.backtracked) {
                                            backtrackInfo = searchResult.backtrackInfo;
                                        }
                                        console.log(`🔍 格式1查找经文: ${currentBook} ${chapterNum}:${v} → ${verseText ? '找到' : '未找到'}`);
                                    }

                                    foundItems.push({
                                        ref: ReferenceFormatManager.formatReference(currentBook, chapterNum, v) + (currentSuffix ? currentSuffix : ''),
                                        text: verseText,
                                        book: currentBook,
                                        chapter: chapterNum,
                                        verse: v,
                                        backtrackInfo: backtrackInfo
                                    });
                                }
                                console.log(`📝 格式1添加到foundItems: ${currentBook} ${chapterNum}:${currentVerseStr}`);
                            }
                        }
                    } else if (match[5] && match[6] && match[7]) { // 格式2 匹配成功: 书名(简)+中文章+阿拉伯节
                        console.log(`🔍 格式2匹配: "${match[0]}", 书名组: "${match[5]}", 章号组: "${match[6]}", 节号组: "${match[7]}", 后缀组: "${match[8]}"`);
                        let rawBook = match[5].replace(/^参/, '');
                        currentBook = bookMap[rawBook] || rawBook;
                        chapterNum = chineseToArabic(match[6]);
                        currentVerseStr = match[7]; // 阿拉伯数字节号，无需 parseChineseVerseRange
                        currentSuffix = match[8];
                        console.log(`📖 格式2解析结果: 书名="${currentBook}", 章号=${chapterNum}, 节号="${currentVerseStr}", 后缀="${currentSuffix}"`);
                        if (!currentBook || chapterNum === 0) {
                            console.log(`❌ 格式2验证失败: 书名="${currentBook}", 章号=${chapterNum}`);
                            continue;
                        }

                        lastBook = currentBook; lastChapter = chapterNum;
                        localBook = currentBook; localChapter = chapterNum;
                        console.log(`✅ 格式2设置最终值: currentBook="${currentBook}", chapterNum=${chapterNum}, currentVerseStr="${currentVerseStr}"`);

                        // 🔧 修复：将格式2的结果添加到foundItems，并获取经文文本
                        const verseNum = parseInt(currentVerseStr, 10);
                        if (!isNaN(verseNum)) {
                            // 🔧 新增：立即查找经文文本，像悬停阅读一样
                            let verseText = null;
                            let backtrackInfo = null;

                            if (withVerses && bibleData) {
                                const searchResult = VerseSearcher.findVerseWithBacktrack(
                                    currentBook, chapterNum, verseNum, bibleData, previousMatches
                                );
                                verseText = searchResult.text;
                                if (searchResult.backtracked) {
                                    backtrackInfo = searchResult.backtrackInfo;
                                }
                                console.log(`🔍 格式2查找经文: ${currentBook} ${chapterNum}:${verseNum} → ${verseText ? '找到' : '未找到'}`);
                            }

                            foundItems.push({
                                ref: ReferenceFormatManager.formatReference(currentBook, chapterNum, verseNum) + (currentSuffix ? currentSuffix : ''),
                                text: verseText,
                                book: currentBook,
                                chapter: chapterNum,
                                verse: verseNum,
                                backtrackInfo: backtrackInfo
                            });
                            console.log(`📝 格式2添加到foundItems: ${currentBook} ${chapterNum}:${verseNum}`);
                        }
                    } else if (match[9] && match[10]) { // 格式3 匹配成功: 中文章+中文节 (依赖上下文)
                        const contextBook = localBook || lastBook; // 优先使用行内上下文，其次使用全局（函数级）上下文
                        if (!contextBook) continue; // 没有上下文无法确定书卷
                        currentBook = contextBook;
                        chapterNum = chineseToArabic(match[9]);
                        currentVerseStr = parseChineseVerseRange(match[10]);
                        currentSuffix = match[11];
                        if (!currentVerseStr || chapterNum === 0) continue;

                        lastBook = currentBook; lastChapter = chapterNum;
                        localBook = currentBook; localChapter = chapterNum; // 如果是行内匹配，也更新行内上下文
                    } else if (match[12] && match[13]) { // 格式4 匹配成功: 中文章+阿拉伯节 (依赖上下文)
                        const contextBook = localBook || lastBook;
                        if (!contextBook) continue;
                        currentBook = contextBook;
                        chapterNum = chineseToArabic(match[12]);
                        currentVerseStr = match[13]; // 阿拉伯数字节号
                        currentSuffix = match[14];
                        if (chapterNum === 0) continue;

                        lastBook = currentBook; lastChapter = chapterNum;
                        localBook = currentBook; localChapter = chapterNum;
                    } else if (match[15] && match[16]) { // 格式5 匹配成功: 书名(简)+阿拉伯章[:：]?阿拉伯节
                        let rawBook = match[15].replace(/^参/, '');
                        currentBook = bookMap[rawBook] || rawBook;
                        let chapVersePart = match[16]; // 形如 "3:16" 或 "3" (单章书卷的节)
                        currentSuffix = match[17];

                        const chapVerseMatch = chapVersePart.match(REGEX.CHAPTER_VERSE_SEPARATOR); // 使用预定义正则
                        if (chapVerseMatch) {
                            chapterNum = parseInt(chapVerseMatch[1], 10);
                            currentVerseStr = chapVerseMatch[2].replace(/^[:：]/, ''); // 移除可能存在的分隔符
                        } else if (singleChapterBooks.has(currentBook)) { // 如果是单章书卷
                            chapterNum = 1; // 章号固定为1
                            currentVerseStr = chapVersePart; // 整段作为节号
                        } else { // 多章书卷，但没有明确章号分隔符 (如 "约 16")，尝试使用上下文中的章
                            const contextChapterP5 = localChapter || lastChapter;
                            if (contextChapterP5) {
                                chapterNum = contextChapterP5;
                                currentVerseStr = chapVersePart; // 整段作为节号
                            } else {
                                // 如果是纯数字且不是范围，可能是章号，但模式要求节号，不明确则跳过
                                if (REGEX.PURE_DIGIT.test(chapVersePart) && !REGEX.DIGIT_RANGE.test(chapVersePart)) {
                                    continue;
                                } else {
                                    continue;
                                }
                            }
                        }
                        if (!currentBook || chapterNum === 0) continue;

                        // 更新上下文
                        lastBook = currentBook;
                        // 如果解析出的章号与当前行内或全局上下文不同，或者行内上下文不存在，则更新它们
                        if (chapterNum !== (localChapter || lastChapter) || !localChapter) {
                            lastChapter = chapterNum;
                            localBook = currentBook; // 同时更新行内书卷上下文
                            localChapter = chapterNum; // 更新行内章上下文
                        }

                    } else if (match[18] && match[19]) { // 新增匹配：单章书卷+节号(无章号)
                        let rawBook = match[18].replace(/^参/, '');
                        currentBook = bookMap[rawBook] || rawBook;

                        // 检查是否为单章书卷
                        if (singleChapterBooks.has(currentBook)) {
                            chapterNum = 1; // 章号固定为1
                            currentVerseStr = match[19]; // 节号
                            currentSuffix = match[20]; // 后缀

                            // 更新上下文
                            lastBook = currentBook;
                            lastChapter = chapterNum;
                            localBook = currentBook;
                            localChapter = chapterNum;
                        } else {
                            // 如果不是单章书卷，可能是格式5的变体，尝试使用上下文章号
                            const contextChapter = localChapter || lastChapter;
                            if (contextChapter) {
                                chapterNum = contextChapter;
                                currentVerseStr = match[19];
                                currentSuffix = match[20];

                                // 更新上下文
                                lastBook = currentBook;
                                localBook = currentBook;
                            } else {
                                continue; // 无法确定章号，跳过
                            }
                        }

                    } else if (match[21] && match[22]) { // 新增匹配：单章书卷+节号(无空格)
                        let rawBook = match[21].replace(/^参/, '');
                        currentBook = bookMap[rawBook] || rawBook;
                        let verseStr = match[22];
                        currentSuffix = match[23]; // 后缀

                        // 明确检查是否为单章书卷的有效引用格式
                        if (isSingleChapterBookReference(currentBook, verseStr)) {
                            chapterNum = 1; // 章号固定为1
                            currentVerseStr = verseStr; // 节号

                            // 更新上下文
                            lastBook = currentBook;
                            lastChapter = chapterNum;
                            localBook = currentBook;
                            localChapter = chapterNum;
                        } else {
                            // 如果不是单章书卷，可能是格式5的变体，尝试使用上下文章号
                            const contextChapter = localChapter || lastChapter;
                            if (contextChapter) {
                                chapterNum = contextChapter;
                                currentVerseStr = verseStr;

                                // 更新上下文
                                lastBook = currentBook;
                                localBook = currentBook;
                                localChapter = contextChapter;
                            } else {
                                currentLine += escapeHtml(match[0]); // 无法确定章号，保留原文
                                lastIndex = match.index + match[0].length;
                                continue; // 无法确定章号，跳过
                            }
                        }
                    } else if (match[26] && match[27] && match[28] && match[29]) { // 🔧 新增格式：跨章引用 - 如"三章一节至四章三十一节"
                        console.log(`🔍 跨章格式匹配: "${match[0]}", 起始章="${match[26]}", 起始节="${match[27]}", 结束章="${match[28]}", 结束节="${match[29]}"`);

                        // 依赖上下文确定书卷
                        const contextBook = localBook || lastBook;
                        if (!contextBook) {
                            console.log(`❌ 跨章格式缺少书卷上下文: contextBook="${contextBook}"`);
                            continue; // 没有书卷上下文无法处理跨章引用
                        }

                        currentBook = contextBook;
                        const startChapter = chineseToArabic(match[26]);
                        const startVerseStr = parseChineseVerseRange(match[27]);
                        const endChapter = chineseToArabic(match[28]);
                        const endVerseStr = parseChineseVerseRange(match[29]);
                        currentSuffix = match[30];

                        if (!startVerseStr || !endVerseStr || startChapter === 0 || endChapter === 0) {
                            console.log(`❌ 跨章格式解析失败: startChapter=${startChapter}, startVerseStr="${startVerseStr}", endChapter=${endChapter}, endVerseStr="${endVerseStr}"`);
                            continue;
                        }

                        console.log(`📖 跨章格式解析结果: 书名="${currentBook}", 起始=${startChapter}:${startVerseStr}, 结束=${endChapter}:${endVerseStr}`);

                        // 处理跨章范围（作为一个整体引用）
                        const startVerse = parseInt(startVerseStr.split('-')[0], 10);
                        const endVerse = parseInt(endVerseStr.includes('-') ? endVerseStr.split('-')[1] : endVerseStr, 10);

                        // 构建跨章引用格式
                        const startRef = ReferenceFormatManager.formatReference(currentBook, startChapter, startVerse);
                        const endRef = ReferenceFormatManager.formatReference(currentBook, endChapter, endVerse);
                        const crossChapterRef = `${startRef}~${endRef}`;

                        // 获取起始和结束经文内容
                        let combinedVerseText = null;
                        if (withVerses && bibleData) {
                            const startSearchResult = VerseSearcher.findVerseWithBacktrack(
                                currentBook, startChapter, startVerse, bibleData, previousMatches
                            );
                            const endSearchResult = VerseSearcher.findVerseWithBacktrack(
                                currentBook, endChapter, endVerse, bibleData, previousMatches
                            );

                            if (startSearchResult.text && endSearchResult.text) {
                                combinedVerseText = `起始：${startSearchResult.text} ... 结束：${endSearchResult.text}`;
                            } else if (startSearchResult.text) {
                                combinedVerseText = `起始：${startSearchResult.text}`;
                            } else if (endSearchResult.text) {
                                combinedVerseText = `结束：${endSearchResult.text}`;
                            }
                        }

                        foundItems.push({
                            ref: crossChapterRef + (currentSuffix ? currentSuffix : ''),
                            text: combinedVerseText,
                            book: currentBook,
                            chapter: startChapter, // 使用起始章作为主要章节
                            verse: startVerse,     // 使用起始节作为主要节
                            isCrossChapter: true,  // 标记为跨章引用
                            endChapter: endChapter,
                            endVerse: endVerse
                        });

                        console.log(`📝 跨章格式添加到foundItems: ${crossChapterRef}`);

                        // 更新上下文为结束章节
                        lastBook = currentBook;
                        lastChapter = endChapter;
                        localBook = currentBook;
                        localChapter = endChapter;

                    } else if (match[32]) { // 🔧 格式9：仅中文节号范围（依赖上下文）- 如"十五至十七节" - 索引更新
                        const chineseVerseRange = match[32];
                        console.log(`🔍 格式9匹配: "${match[0]}", 中文节号范围: "${chineseVerseRange}"`);

                        // 🔧 添加上下文过滤：避免匹配普通文本中的中文数字
                        const startIndex = match.index;
                        const endIndex = match.index + match[0].length;
                        const prevChar = startIndex > 0 ? line[startIndex - 1] : '^';
                        const nextChar = endIndex < line.length ? line[endIndex] : '$';

                        // 检查是否像列表项：中文数字，前面是行首，后面是全角空格
                        const looksLikeChineseList =
                            (prevChar === '^' || /^\s*$/.test(prevChar)) &&
                            /^[　\s\t]/.test(nextChar);

                        // 检查是否在不合适的上下文中
                        const isInappropriateContext =
                            // 列表项检测：如"一　"、"二　"、"三　"等
                            looksLikeChineseList ||
                            // 前面是"第"、"这"、"那"等
                            /[第这那其]$/.test(prevChar) ||
                            // 前面是"用"、"有"、"是"等
                            /[用有是为在于]$/.test(prevChar) ||
                            // 前面是"之"、"的"、"了"等助词
                            /[之的了得过来去]$/.test(prevChar) ||
                            // 后面是"章"、"个"、"种"、"样"、"言"、"语"、"组"、"类"、"部"、"方"等
                            /^[章个种样言语次遍回趟组类部方面点条项]/.test(nextChar) ||
                            // 后面是"天"、"年"、"月"、"日"、"时"等时间单位
                            /^[天年月日时分秒]/.test(nextChar) ||
                            // 后面是"位"、"本"、"段"、"对"、"篇"等量词
                            /^[位本段对篇首]/.test(nextChar) ||
                            // 后面是"神"、"全"、"我"等常见段落开头词汇（序号后的内容）
                            /^[神全我人主耶基督圣灵父子爱信望盼救恩生命真理道路]/.test(nextChar) ||
                            // 前后都是中文字符（可能是词语的一部分）
                            (/[\u4e00-\u9fff]$/.test(prevChar) && /^[\u4e00-\u9fff]/.test(nextChar)) ||
                            // 前面是中文字符且不是明确的经文引用上下文（排除连接词）
                            (/[\u4e00-\u9fff]$/.test(prevChar) && !/[章节和与及，、；]$/.test(prevChar));

                        if (isInappropriateContext) {
                            console.log(`❌ 格式9过滤: "${match[0]}"在不合适的上下文中，前字符="${prevChar}", 后字符="${nextChar}"`);
                            continue;
                        }

                        // 依赖上下文确定书卷和章节
                        const contextBook = localBook || lastBook;
                        const contextChapter = localChapter || lastChapter;

                        if (!contextBook || !contextChapter) {
                            console.log(`❌ 格式9缺少上下文: contextBook="${contextBook}", contextChapter=${contextChapter}`);
                            continue; // 没有上下文无法确定书卷和章节
                        }

                        currentBook = contextBook;
                        chapterNum = contextChapter;
                        currentSuffix = match[33];

                        // 解析中文节号范围
                        const verseRangeStr = parseChineseVerseRange(chineseVerseRange);
                        if (verseRangeStr) {
                            console.log(`📖 格式9解析结果: 书名="${currentBook}", 章号=${chapterNum}, 节号范围="${verseRangeStr}"`);

                            // 解析范围字符串为起始和结束节号
                            let startVerse, endVerse;
                            if (verseRangeStr.includes('-')) {
                                const parts = verseRangeStr.split('-');
                                startVerse = parseInt(parts[0], 10);
                                endVerse = parseInt(parts[1], 10);
                            } else {
                                startVerse = endVerse = parseInt(verseRangeStr, 10);
                            }

                            // 处理范围内的每个节号
                            for (let v = startVerse; v <= endVerse; v++) {
                                // 立即查找经文文本
                                let verseText = null;
                                let backtrackInfo = null;

                                if (withVerses && bibleData) {
                                    const searchResult = VerseSearcher.findVerseWithBacktrack(
                                        currentBook, chapterNum, v, bibleData, previousMatches
                                    );
                                    verseText = searchResult.text;
                                    if (searchResult.backtracked) {
                                        backtrackInfo = searchResult.backtrackInfo;
                                    }
                                    console.log(`🔍 格式9查找经文: ${currentBook} ${chapterNum}:${v} → ${verseText ? '找到' : '未找到'}`);
                                }

                                foundItems.push({
                                    ref: ReferenceFormatManager.formatReference(currentBook, chapterNum, v) + (currentSuffix ? currentSuffix : ''),
                                    text: verseText,
                                    book: currentBook,
                                    chapter: chapterNum,
                                    verse: v,
                                    backtrackInfo: backtrackInfo
                                });
                            }
                            console.log(`📝 格式9添加到foundItems: ${currentBook} ${chapterNum}:${startVerse}-${endVerse}`);
                        } else {
                            console.log(`❌ 格式9无法解析中文节号范围: "${chineseVerseRange}"`);
                            continue;
                        }
                    } else if (match[34]) { // 格式10 匹配成功: 仅阿拉伯节号 (支持回溯查找) - 注意索引变更
                        const verseStrOnly = match[34];
                        console.log(`🔍 格式6匹配: "${match[0]}", 节号: "${verseStrOnly}"`);
                        console.log(`📊 当前上下文状态: localBook="${localBook}", localChapter=${localChapter}, lastBook="${lastBook}", lastChapter=${lastChapter}`);
                        console.log(`📚 一步完成-历史匹配记录:`, previousMatches.map(m => `${m.book} ${m.chapter}:${m.verse}${m.isContext ? '(上下文)' : ''}`));
                        console.log(`📚 一步完成-历史匹配记录详细:`, previousMatches);

                        // 🔧 修复：智能选择最合适的上下文 - 基于上下文新鲜度
                        let contextBook = null;
                        let contextChapter = null;

                        // 获取当前上下文
                        const currentContext = {
                            book: localBook || lastBook,
                            chapter: localChapter || lastChapter
                        };

                        // 🔧 修复：获取历史匹配中的最新上下文（优先考虑最新记录）
                        let latestHistoryContext = null;
                        if (previousMatches.length > 0) {
                            // 首先尝试使用最后一个记录（最新的引用）
                            const lastMatch = previousMatches[previousMatches.length - 1];
                            console.log(`🔍 一步完成-最后一个记录:`, lastMatch);

                            latestHistoryContext = {
                                book: lastMatch.book,
                                chapter: lastMatch.chapter,
                                isBookOnly: lastMatch.isBookOnly || false,
                                index: previousMatches.length - 1,
                                isLatestMatch: true
                            };
                            console.log(`🔍 一步完成-初始历史上下文:`, latestHistoryContext);

                            // 如果最后一个记录不是上下文记录，也查找最后一个上下文记录作为备选
                            if (!lastMatch.isContext) {
                                console.log(`🔍 一步完成-最后一个记录不是上下文记录，查找上下文记录`);
                                for (let i = previousMatches.length - 1; i >= 0; i--) {
                                    if (previousMatches[i].isContext) {
                                        console.log(`🔍 一步完成-找到上下文记录 at ${i}:`, previousMatches[i]);
                                        // 如果找到的上下文记录比最后一个记录更新，使用上下文记录
                                        if (i > previousMatches.length - 3) { // 允许一定的容差
                                            latestHistoryContext = {
                                                book: previousMatches[i].book,
                                                chapter: previousMatches[i].chapter,
                                                isBookOnly: previousMatches[i].isBookOnly || false,
                                                index: i,
                                                isLatestMatch: false
                                            };
                                            console.log(`🔍 一步完成-使用上下文记录:`, latestHistoryContext);
                                        } else {
                                            console.log(`🔍 一步完成-上下文记录太远，保持使用最新记录`);
                                        }
                                        break;
                                    }
                                }
                            } else {
                                console.log(`🔍 一步完成-最后一个记录就是上下文记录`);
                            }
                        }

                        // 🔧 修复：智能上下文选择 - 平衡当前上下文和历史上下文
                        // 1. 如果历史上下文比当前上下文更新，使用历史上下文
                        // 2. 如果当前上下文有效且没有更新的历史上下文，使用当前上下文
                        // 3. 如果当前上下文无效，使用历史上下文

                        // 🔧 修复：优先使用当前上下文，只有当前上下文无效时才使用历史上下文
                        console.log(`🔍 一步完成-上下文比较: 当前(${currentContext.book} ${currentContext.chapter}) vs 历史(${latestHistoryContext?.book} ${latestHistoryContext?.chapter})`);

                        if (currentContext.book && currentContext.chapter) {
                            // 🔧 修复：优先使用当前上下文（行内或全局上下文）
                            contextBook = currentContext.book;
                            contextChapter = currentContext.chapter;
                            console.log(`📊 一步完成-使用当前上下文: contextBook="${contextBook}", contextChapter=${contextChapter} for 节号 ${verseStrOnly}`);
                            console.log(`📋 一步完成-选择原因: 当前上下文有效，优先使用`);
                        } else if (latestHistoryContext) {
                            // 当前上下文无效，使用历史上下文
                            contextBook = latestHistoryContext.book;
                            contextChapter = latestHistoryContext.chapter;
                            isBacktracked = true;
                            backtrackInfo = `使用历史上下文: ${contextBook} ${contextChapter}章`;
                            console.log(`🔄 一步完成-当前上下文无效，使用历史上下文: ${contextBook} ${contextChapter}章${latestHistoryContext.isBookOnly ? '(单独书名)' : ''} for 节号 ${verseStrOnly}`);
                            console.log(`📋 一步完成-选择原因: 当前上下文无效`);
                        } else {
                            console.log(`❌ 一步完成-无可用上下文 for 节号 ${verseStrOnly}`);
                        }

                        if (!contextBook || !contextChapter) {
                            // 如果仍然没有上下文，则不处理纯数字节号，避免误匹配
                            continue;
                        }

                        // 防止将列表编号 (如 "1." "2、") 误认为节号
                        const startIndex = match.index; // 匹配项在行中的起始位置
                        const prevChar = startIndex > 0 ? line[startIndex - 1] : '^'; // 匹配项前的字符 (或行首)
                        let nextCharIndex = match.index + match[0].length; // 匹配项后的字符索引
                        const nextChar = line[nextCharIndex] || '$'; // 匹配项后的字符 (或行尾)

                        // 判断是否像列表项：纯数字，前后是空格/标点/行首尾
                        const looksLikeList = REGEX.PURE_DIGIT.test(verseStrOnly) &&
                            (prevChar === '^' || REGEX.LOOKS_LIKE_LIST_PREV.test(prevChar)) &&
                            (nextChar === '.' || nextChar === '、' || REGEX.LOOKS_LIKE_LIST_NEXT.test(nextChar) || nextChar === '$');

                        // 判断前面是否有明确的书卷章节上下文 (例如 "约翰福音三章1" 或 "约3:1")
                        // \S 匹配任何非空白字符，确保书卷/章号和节号之间没有意外的空白
                        const hasBookChapterContextImmediatelyBefore =
                            line.substring(0, startIndex).match(REGEX.CHAPTER_CONTEXT) ||
                            line.substring(0, startIndex).match(REGEX.BOOK_CHAPTER_CONTEXT);

                        if (looksLikeList && !hasBookChapterContextImmediatelyBefore) {
                            // 如果看起来像列表项，并且前面没有紧邻的书卷章节上下文，则跳过
                            continue;
                        }

                        currentBook = contextBook;
                        chapterNum = contextChapter;
                        currentVerseStr = verseStrOnly;
                        currentSuffix = match[35];
                        console.log(`✅ 格式6设置最终值: currentBook="${currentBook}", chapterNum=${chapterNum}, currentVerseStr="${currentVerseStr}"`);
                    } else if (match[29] && match[30]) { // 新增格式：书名(全名)+中文节号（无章号，适用于单章书卷）- 如"犹大书一节"
                        console.log(`🔍 新格式匹配（书名+中文节号，无章号）: "${match[0]}", 书名组: "${match[29]}", 节号组: "${match[30]}"`);
                        const bookNameOrAbbr = match[29].replace(/^参/, ''); // 移除可选的 "参" 前缀
                        currentBook = fullBookNameToAbbrMap[bookNameOrAbbr] || bookMap[bookNameOrAbbr] || bookNameOrAbbr;
                        console.log(`📖 书名映射: "${bookNameOrAbbr}" → "${currentBook}"`);

                        if (!currentBook) {
                            console.log(`❌ 新格式无效书名: "${bookNameOrAbbr}"`);
                            continue; // 无效书名，跳过
                        }

                        // 检查是否可能是章号引用的误匹配
                        const fullMatch = match[0];
                        const matchStartIndex = match.index;
                        const matchEndIndex = matchStartIndex + fullMatch.length;
                        const nextChar = line[matchEndIndex] || '';

                        // 更严格的章号引用检测
                        const isLikelyChapterReference =
                            fullMatch.includes('章') || // 包含"章"字
                            nextChar === '章' || // 后面紧跟"章"字
                            (match[30] && /^[一二三四五六七八九十百千〇零]+$/.test(match[30]) &&
                                !fullMatch.includes('节') && !singleChapterBooks.has(currentBook) &&
                                (nextChar === '章' || nextChar === '来' || nextChar === '看' || nextChar === '说')); // 常见的章号引用后续词

                        if (isLikelyChapterReference) {
                            console.log(`⚠️ 疑似章号引用误匹配，跳过新格式处理: "${fullMatch}", 后续字符: "${nextChar}"`);
                            continue; // 跳过，让其他格式或上下文处理
                        }

                        // 检查是否为单章书卷
                        if (singleChapterBooks.has(currentBook)) {
                            console.log(`✅ 识别为单章书卷: "${currentBook}"`);
                            chapterNum = 1; // 单章书卷固定为第1章
                            currentVerseStr = parseChineseVerseRange(match[30]); // 解析中文节号或范围
                            currentSuffix = match[31]; // 获取后缀 "下"
                            console.log(`📝 解析结果: 章号=${chapterNum}, 节号="${currentVerseStr}", 后缀="${currentSuffix}"`);

                            if (!currentVerseStr) {
                                console.log(`❌ 无效节号: "${match[30]}"`);
                                continue; // 无效节号，跳过
                            }

                            // 更新上下文
                            lastBook = currentBook;
                            lastChapter = chapterNum;
                            localBook = currentBook;
                            localChapter = chapterNum;
                        } else {
                            console.log(`⚠️ 非单章书卷且非明确节号引用，跳过新格式处理: "${currentBook}"`);
                            continue; // 非单章书卷的情况，跳过新格式，让其他格式处理
                        }
                    } else {
                        continue; // 未匹配到任何已知格式
                    }

                    // 如果成功解析出书、章、节
                    if (currentBook && chapterNum > 0 && currentVerseStr) {
                        const rangeMatch = currentVerseStr.match(REGEX.AR_NUM_RANGE_MATCH); // 使用预定义的阿拉伯数字范围正则
                        if (rangeMatch) { // 如果是范围
                            const startVerse = parseInt(rangeMatch[1], 10);
                            const endVerse = parseInt(rangeMatch[2], 10);
                            if (!isNaN(startVerse) && !isNaN(endVerse) && startVerse <= endVerse) {
                                for (let v = startVerse; v <= endVerse; v++) { // 展开范围内的每一节
                                    const pRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, v) + (currentSuffix ? currentSuffix : ''); // 格式化后的引用

                                    let sTxt = null;
                                    let finalBacktrackInfo = null;

                                    if (withVerses) {
                                        // 使用回溯查找功能
                                        const searchResult = VerseSearcher.findVerseWithBacktrack(
                                            currentBook, chapterNum, v, bibleData, previousMatches
                                        );
                                        sTxt = searchResult.text;
                                        if (searchResult.backtracked) {
                                            finalBacktrackInfo = searchResult.backtrackInfo;
                                        } else if (isBacktracked && backtrackInfo) {
                                            // 如果是通过历史上下文找到的，也记录这个信息
                                            finalBacktrackInfo = backtrackInfo;
                                        }
                                    }

                                    foundItems.push({
                                        ref: pRef,
                                        text: sTxt,
                                        book: currentBook,
                                        chapter: chapterNum,
                                        verse: v,
                                        backtrackInfo: finalBacktrackInfo
                                    });
                                }
                            }
                        } else { // 如果是单个节号
                            const singleVerseMatch = currentVerseStr.match(REGEX.SINGLE_AR_NUM); // 使用预定义的单个阿拉伯数字正则
                            if (singleVerseMatch) {
                                const verseNum = parseInt(singleVerseMatch[1], 10);
                                if (!isNaN(verseNum)) {
                                    const pRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, verseNum) + (currentSuffix ? currentSuffix : '');

                                    let sTxt = null;
                                    let finalBacktrackInfo = null;

                                    if (withVerses) {
                                        // 使用回溯查找功能
                                        const searchResult = VerseSearcher.findVerseWithBacktrack(
                                            currentBook, chapterNum, verseNum, bibleData, previousMatches
                                        );
                                        sTxt = searchResult.text;
                                        if (searchResult.backtracked) {
                                            finalBacktrackInfo = searchResult.backtrackInfo;
                                        } else if (isBacktracked && backtrackInfo) {
                                            // 如果是通过历史上下文找到的，也记录这个信息
                                            finalBacktrackInfo = backtrackInfo;
                                        }
                                    }

                                    foundItems.push({
                                        ref: pRef,
                                        text: sTxt,
                                        book: currentBook,
                                        chapter: chapterNum,
                                        verse: verseNum,
                                        backtrackInfo: finalBacktrackInfo
                                    });
                                }
                            }
                        }
                    }
                } // 完成当前行所有引用的查找

                // 更新历史匹配记录（用于回溯查找）
                if (foundItems.length > 0) {
                    foundItems.forEach(item => {
                        // 🔧 修复：只要有明确的书卷章节信息就添加到历史记录，不要求必须找到经文内容
                        if (item.book && item.chapter) {
                            previousMatches.push({
                                book: item.book,
                                chapter: item.chapter,
                                verse: item.verse
                            });
                            console.log(`📝 添加到历史匹配记录: ${item.book} ${item.chapter}:${item.verse}`);
                        }
                    });

                    // 只保留最近的10个匹配记录，避免内存过度使用
                    if (previousMatches.length > 10) {
                        previousMatches.splice(0, previousMatches.length - 10);
                    }

                    // 记录当前行的上下文信息到段落上下文中
                    if (localBook && localChapter) {
                        paragraphContexts.push({
                            book: localBook,
                            chapter: localChapter,
                            lineIndex: lines.indexOf(line)
                        });

                        // 只保留最近的5个段落上下文
                        if (paragraphContexts.length > 5) {
                            paragraphContexts.shift();
                        }
                    }
                }

                // 对当前行找到的引用进行去重 (基于 ref 字符串)
                const uniqueOutputItems = [];
                const seenRefs = new Set(); // 用于记录已添加的引用，实现去重

                // 智能验证和修正（如果启用）
                const enableSmartCorrection = document.getElementById('enableSmartCorrection')?.checked !== false;
                const showCorrectionNotes = document.getElementById('showCorrectionNotes')?.checked !== false;
                const strictVerseValidation = document.getElementById('strictVerseValidation')?.checked !== false;

                // 先对项目进行校验和修正，再去重
                const correctedItems = [];
                for (const item of foundItems) {
                    // 创建一个新项目进行潜在修正
                    const correctedItem = { ...item, correctionNote: null, isInvalid: false };

                    if (enableSmartCorrection) {
                        // 1. 检查书卷是否有效
                        if (!bookChapterLimits[correctedItem.book]) {
                            // 尝试找到最接近的书卷名
                            const bookNames = Object.keys(bookChapterLimits);
                            const similarBook = findMostSimilarString(correctedItem.book, bookNames);
                            if (similarBook) {
                                correctedItem.correctionNote = `原书卷"${correctedItem.book}"已更正为"${similarBook}"`;
                                correctedItem.book = similarBook;
                                // 更新引用文本
                                correctedItem.ref = correctedItem.ref.replace(/^[^ ]+/, similarBook);
                            }
                        }

                        // 2. 检查章节是否在范围内
                        const maxChapter = bookChapterLimits[correctedItem.book] || 150; // 使用150作为最大值的保守估计
                        if (correctedItem.chapter > maxChapter) {
                            if (maxChapter === 1) {
                                // 对于单章书卷，把章号当作节号处理
                                correctedItem.verse = correctedItem.chapter;
                                correctedItem.chapter = 1;
                                correctedItem.correctionNote = (correctedItem.correctionNote ? correctedItem.correctionNote + "; " : "") +
                                    `${correctedItem.book}为单章书卷，已将章号"${correctedItem.chapter}"改为节号`;
                                // 更新引用文本
                                correctedItem.ref = ReferenceFormatManager.formatReference(correctedItem.book, 1, correctedItem.verse) + (item.ref.includes('下') ? '下' : '');
                            } else {
                                // 如果章号超出范围，尝试使用最大章号
                                correctedItem.correctionNote = (correctedItem.correctionNote ? correctedItem.correctionNote + "; " : "") +
                                    `章号超出范围(最大${maxChapter}章)，已更正为最大章号，如果不对，请手动修改`;
                                correctedItem.chapter = maxChapter;
                                // 更新引用文本
                                const parts = correctedItem.ref.split(':');
                                if (parts.length > 1) {
                                    parts[0] = `${correctedItem.book} ${maxChapter}`;
                                    correctedItem.ref = parts.join(':');
                                }
                            }
                        }

                        // 3. 如果启用了严格验证且有JSON数据，则验证节号
                        if (strictVerseValidation && bibleData) {
                            // 获取该章的最大节数
                            let maxVerse = 0;
                            // 构建一个表达式来匹配此书此章的所有节
                            const verseRegex = new RegExp(`^${correctedItem.book} ${correctedItem.chapter}:(\\d+)$`);

                            // 如果verseCountData中已有该书章的数据，直接使用
                            const bookChapterKey = `${correctedItem.book}_${correctedItem.chapter}`;
                            if (DataManager.verseCountData[bookChapterKey]) {
                                maxVerse = DataManager.verseCountData[bookChapterKey];
                            } else {
                                // 否则计算该章的最大节数
                                for (const key in bibleData) {
                                    if (key.startsWith(`${correctedItem.book} ${correctedItem.chapter}:`)) {
                                        const match = key.match(verseRegex);
                                        if (match) {
                                            const verseNum = parseInt(match[1], 10);
                                            maxVerse = Math.max(maxVerse, verseNum);
                                        }
                                    }
                                }
                                // 保存到缓存
                                DataManager.verseCountData[bookChapterKey] = maxVerse;
                            }

                            // 验证节号
                            if (maxVerse > 0 && correctedItem.verse > maxVerse) {
                                if (correctedItem.verse <= maxVerse + 5) {
                                    // 如果节号仅略微超出范围，尝试修正为最大节号
                                    correctedItem.correctionNote = (correctedItem.correctionNote ? correctedItem.correctionNote + "; " : "") +
                                        `节号超出范围(最大${maxVerse}节)，已更正，`;
                                    correctedItem.verse = maxVerse;
                                    // 更新引用文本
                                    correctedItem.ref = ReferenceFormatManager.formatReference(correctedItem.book, correctedItem.chapter, maxVerse) + (item.ref.includes('下') ? '下' : '');
                                    // 更新文本键以重新查找经文
                                    correctedItem.text = bibleData[`${correctedItem.book} ${correctedItem.chapter}:${maxVerse}`] || null;
                                } else {
                                    // 如果节号超出范围太多，标记为无效但不修改
                                    correctedItem.isInvalid = true;
                                    correctedItem.correctionNote = `节号${correctedItem.verse}明显超出范围(最大${maxVerse}节)`;
                                }
                            }
                        }
                    }

                    correctedItems.push(correctedItem);
                }

                // 处理修正后的项目并去重
                for (const item of correctedItems) {
                    if (!seenRefs.has(item.ref)) {
                        uniqueOutputItems.push(item);
                        seenRefs.add(item.ref);
                    }
                }

                // 将去重后的引用（保持原始匹配顺序）添加到最终输出
                if (uniqueOutputItems.length > 0) {
                    htmlBuilder.add(`<div class="processed-references">`); // 包裹所有找到的引用
                    uniqueOutputItems.forEach(item => {
                        if (!withVerses) {
                            // 格式化引用模式：只输出纯文本引用，不加悬浮
                            if (item.correctionNote && showCorrectionNotes) {
                                // 如果有修正且启用显示修正提示，添加修正标记
                                htmlBuilder.add(`<span class="auto-corrected">${escapeHtml(item.ref)}`);
                                htmlBuilder.add(`<span class="correction-note">${escapeHtml(item.correctionNote)}</span></span>\n`);
                            } else {
                                htmlBuilder.add(`${escapeHtml(item.ref)}\n`);
                            }
                        } else {
                            // 带经文模式：悬浮预览引用
                            if (item.correctionNote && showCorrectionNotes) {
                                // 如果有修正且启用显示修正提示，将修正注释添加到悬停预览中
                                htmlBuilder.add(`<span class="reference-hover auto-corrected">${escapeHtml(item.ref)}`);
                                htmlBuilder.add(`<span class="correction-note">${escapeHtml(item.correctionNote)}</span>`);
                            } else {
                                htmlBuilder.add(`<span class="reference-hover">${escapeHtml(item.ref)}`);
                            }

                            htmlBuilder.add(`<span class="verse-preview">`);
                            htmlBuilder.add(`<div class="verse-preview-header">${escapeHtml(item.ref)}</div>`);

                            if (item.correctionNote && showCorrectionNotes) {
                                htmlBuilder.add(`<div style="color:#856404;margin-bottom:5px;font-size:0.9em;background:#fff8e5;padding:3px;border-radius:3px;">修正: ${escapeHtml(item.correctionNote)}</div>`);
                            }

                            if (item.isInvalid) {
                                htmlBuilder.add(`<div class="verse-preview-not-found">无效引用: ${escapeHtml(item.correctionNote)}</div>`);
                            } else if (item.text) {
                                htmlBuilder.add(`<div class="verse-preview-text">${escapeHtml(item.text)}</div>`);
                            } else {
                                // 检查是否是因为章节超出范围导致找不到经文
                                if (item.correctionNote && (item.correctionNote.includes('章号超出范围') || item.correctionNote.includes('节号超出范围'))) {
                                    htmlBuilder.add(`<div class="verse-preview-not-found">无效引用: ${escapeHtml(item.correctionNote)}</div>`);
                                } else {
                                    htmlBuilder.add(`<div class="verse-preview-not-found">经文未在JSON中找到</div>`);
                                }
                            }

                            htmlBuilder.add(`</span>`);
                            htmlBuilder.add(`</span>`);

                            if (item.isInvalid) {
                                // 对于无效引用，显示特殊样式
                                htmlBuilder.add(`<span class="scripture-invalid">\t${escapeHtml(item.correctionNote || '无效引用')}</span>\n`);
                            } else if (item.text) {
                                htmlBuilder.add(`<span class="scripture-text">\t${escapeHtml(item.text)}</span>\n`);
                                // 如果是通过回溯找到的，显示回溯信息
                                if (item.backtrackInfo) {
                                    htmlBuilder.add(`<span class="scripture-backtrack-info" style="color: #856404; font-size: 0.85em; font-style: italic;">\t[${escapeHtml(item.backtrackInfo)}]</span>\n`);
                                }
                            } else {
                                // 检查是否是因为章节超出范围导致找不到经文
                                if (item.correctionNote && (item.correctionNote.includes('章号超出范围') || item.correctionNote.includes('节号超出范围'))) {
                                    htmlBuilder.add(`<span class="scripture-invalid">\t${escapeHtml(item.correctionNote)}</span>\n`);
                                } else {
                                    htmlBuilder.add(`<span class="scripture-not-found">  -> (经文未在JSON中找到)</span>\n`);
                                    // 如果有回溯信息但没找到经文，也显示回溯尝试信息
                                    if (item.backtrackInfo) {
                                        htmlBuilder.add(`<span class="scripture-backtrack-info" style="color: #856404; font-size: 0.85em; font-style: italic;">\t[${escapeHtml(item.backtrackInfo)}]</span>\n`);
                                    }
                                }
                            }
                        }
                    });
                    htmlBuilder.add(`</div>`); // 关闭 processed-references div
                }
                htmlBuilder.add(`</div>`); // 关闭 line-group div
            } // 完成所有行的处理

            const duration = PerformanceMonitor.endTimer('processText');
            console.log(`processText 执行时间: ${duration.toFixed(2)}ms`);

            return htmlBuilder.toString(); // 使用高性能构建器输出
        }

        // --- 新增函数：仅从已格式化文本中查找经文 ---
        // 此函数专用于"步骤2：查找经文"。
        // 它假设输入行或者是标准格式 "书卷简称 章号:节号" (例如 "罗 12:2")，

        function lookupVersesFromFormattedText(text, bibleDataToUse) {
            return ErrorHandler.safeExecute(() => {
                // 参数验证
                if (!TypeValidator.isValidString(text, true)) {
                    return `<div class="line-group"><div class="scripture-not-found">错误：输入文本为空或无效。</div></div>`;
                }

                if (!TypeValidator.isValidObject(bibleDataToUse)) {
                    return `<div class="line-group"><div class="scripture-not-found">错误：请先成功加载经文 JSON 文件。</div></div>`;
                }

                const lines = text.split('\n'); // 按行分割文本
                let processedHtml = ''; // 存储处理后的 HTML 输出

                // 使用优化的正则表达式工厂创建书卷正则部分（仅标准简称）
                const standardBookMap = Object.keys(bookMap)
                    .filter(abbr => bookMap[abbr] === abbr)
                    .reduce((acc, key) => {
                        acc[key] = bookMap[key];
                        return acc;
                    }, {});

                const bookAbbrRegexPart = RegexFactory.createBookRegexPart(standardBookMap);

                // 使用工厂方法创建优化的正则表达式
                const simpleRefRegex = RegexFactory.createStandardReferenceRegex(bookAbbrRegexPart);
                const singleChapterRefRegex = RegexFactory.createSingleChapterRegex(bookAbbrRegexPart, true);
                const singleChapterNoSpaceRefRegex = RegexFactory.createSingleChapterRegex(bookAbbrRegexPart, false);

                for (const line of lines) { // 逐行处理
                    processedHtml += `<div class="line-group">`; // 每行输入对应一个 line-group

                    let match; // 用于存储 simpleRefRegex 的匹配结果
                    const foundItems = []; // 存储当前行找到的所有经文对象
                    simpleRefRegex.lastIndex = 0; // 重置正则的匹配位置
                    singleChapterRefRegex.lastIndex = 0; // 重置单章书卷正则的匹配位置
                    singleChapterNoSpaceRefRegex.lastIndex = 0; // 重置单章书卷正则的匹配位置
                    const trimmedLine = line.trim(); // 对行进行trim，以处理可能的首尾空格

                    let isPureFormattedReferenceLine = false; // 标记当前行是否为纯粹的格式化引用

                    // 测试整行是否匹配标准引用格式
                    if (simpleRefRegex.test(trimmedLine)) {
                        isPureFormattedReferenceLine = true; // 如果整行是标准引用
                        simpleRefRegex.lastIndex = 0; // 重置 regex
                        while ((match = simpleRefRegex.exec(trimmedLine)) !== null) {
                            const currentBook = match[1];
                            const chapterNum = parseInt(match[2], 10);
                            const currentVerseStr = match[3];
                            const currentSuffix = match[4] || '';

                            if (!currentBook || isNaN(chapterNum) || chapterNum <= 0 || !currentVerseStr) {
                                continue;
                            }

                            const rangeMatch = currentVerseStr.match(REGEX.AR_NUM_RANGE_MATCH); // 使用预定义的阿拉伯数字范围正则
                            if (rangeMatch) {
                                const startVerse = parseInt(rangeMatch[1], 10);
                                const endVerse = parseInt(rangeMatch[2], 10);
                                if (!isNaN(startVerse) && !isNaN(endVerse) && startVerse <= endVerse) {
                                    for (let v = startVerse; v <= endVerse; v++) {
                                        const pRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, v) + currentSuffix;
                                        const sTxtKey = `${currentBook} ${chapterNum}:${v}`;
                                        const sTxt = bibleDataToUse[sTxtKey] || null;
                                        foundItems.push({ ref: pRef, text: sTxt, book: currentBook, chapter: chapterNum, verse: v });
                                    }
                                }
                            } else {
                                const singleVerseMatch = currentVerseStr.match(REGEX.SINGLE_AR_NUM); // 使用预定义的单个阿拉伯数字正则
                                if (singleVerseMatch) {
                                    const verseNum = parseInt(singleVerseMatch[1], 10);
                                    if (!isNaN(verseNum)) {
                                        const pRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, verseNum) + currentSuffix;
                                        const sTxtKey = `${currentBook} ${chapterNum}:${verseNum}`;
                                        const sTxt = bibleDataToUse[sTxtKey] || null;
                                        foundItems.push({ ref: pRef, text: sTxt, book: currentBook, chapter: chapterNum, verse: verseNum });
                                    }
                                }
                            }
                        }
                    }

                    // 检查是否匹配单章书卷引用格式（有空格版本）
                    if (!isPureFormattedReferenceLine && singleChapterRefRegex.test(trimmedLine)) {
                        isPureFormattedReferenceLine = true; // 标记为格式化引用行
                        singleChapterRefRegex.lastIndex = 0; // 重置正则

                        while ((match = singleChapterRefRegex.exec(trimmedLine)) !== null) {
                            const currentBook = match[1];
                            const currentVerseStr = match[2];
                            const currentSuffix = match[3] || '';

                            // 检查是否为单章书卷
                            if (singleChapterBooks.has(currentBook)) {
                                isPureFormattedReferenceLine = true; // 标记为格式化引用行
                                const chapterNum = 1; // 单章书卷固定为第1章

                                const rangeMatch = currentVerseStr.match(REGEX.AR_NUM_RANGE_MATCH);
                                if (rangeMatch) {
                                    const startVerse = parseInt(rangeMatch[1], 10);
                                    const endVerse = parseInt(rangeMatch[2], 10);
                                    if (!isNaN(startVerse) && !isNaN(endVerse) && startVerse <= endVerse) {
                                        for (let v = startVerse; v <= endVerse; v++) {
                                            const pRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, v) + currentSuffix;
                                            const sTxtKey = `${currentBook} ${chapterNum}:${v}`;
                                            const sTxt = bibleDataToUse[sTxtKey] || null;
                                            foundItems.push({ ref: pRef, text: sTxt, book: currentBook, chapter: chapterNum, verse: v });
                                        }
                                    }
                                } else {
                                    const singleVerseMatch = currentVerseStr.match(REGEX.SINGLE_AR_NUM);
                                    if (singleVerseMatch) {
                                        const verseNum = parseInt(singleVerseMatch[1], 10);
                                        if (!isNaN(verseNum)) {
                                            const pRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, verseNum) + currentSuffix;
                                            const sTxtKey = `${currentBook} ${chapterNum}:${verseNum}`;
                                            const sTxt = bibleDataToUse[sTxtKey] || null;
                                            foundItems.push({ ref: pRef, text: sTxt, book: currentBook, chapter: chapterNum, verse: verseNum });
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 检查是否匹配单章书卷引用格式（无空格版本）
                    if (!isPureFormattedReferenceLine && singleChapterNoSpaceRefRegex.test(trimmedLine)) {
                        isPureFormattedReferenceLine = true; // 标记为格式化引用行
                        singleChapterNoSpaceRefRegex.lastIndex = 0; // 重置正则

                        while ((match = singleChapterNoSpaceRefRegex.exec(trimmedLine)) !== null) {
                            const currentBook = match[1];
                            const currentVerseStr = match[2];
                            const currentSuffix = match[3] || '';

                            // 检查是否为单章书卷
                            if (singleChapterBooks.has(currentBook)) {
                                isPureFormattedReferenceLine = true; // 标记为格式化引用行
                                const chapterNum = 1; // 单章书卷固定为第1章

                                const rangeMatch = currentVerseStr.match(REGEX.AR_NUM_RANGE_MATCH);
                                if (rangeMatch) {
                                    const startVerse = parseInt(rangeMatch[1], 10);
                                    const endVerse = parseInt(rangeMatch[2], 10);
                                    if (!isNaN(startVerse) && !isNaN(endVerse) && startVerse <= endVerse) {
                                        for (let v = startVerse; v <= endVerse; v++) {
                                            const pRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, v) + currentSuffix;
                                            const sTxtKey = `${currentBook} ${chapterNum}:${v}`;
                                            const sTxt = bibleDataToUse[sTxtKey] || null;
                                            foundItems.push({ ref: pRef, text: sTxt, book: currentBook, chapter: chapterNum, verse: v });
                                        }
                                    }
                                } else {
                                    const singleVerseMatch = currentVerseStr.match(REGEX.SINGLE_AR_NUM);
                                    if (singleVerseMatch) {
                                        const verseNum = parseInt(singleVerseMatch[1], 10);
                                        if (!isNaN(verseNum)) {
                                            const pRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, verseNum) + currentSuffix;
                                            const sTxtKey = `${currentBook} ${chapterNum}:${verseNum}`;
                                            const sTxt = bibleDataToUse[sTxtKey] || null;
                                            foundItems.push({ ref: pRef, text: sTxt, book: currentBook, chapter: chapterNum, verse: verseNum });
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 如果当前行不是纯粹的格式化引用行，并且不为空，则将其视为"原始文本"并包裹
                    if (!isPureFormattedReferenceLine && trimmedLine !== '') {
                        processedHtml += `<div class="original-line-wrapper">${escapeHtml(line)}</div>`;
                    }

                    // 将找到的引用（如果有）添加到输出 HTML
                    if (foundItems.length > 0) {
                        processedHtml += `<div class="processed-references">`;

                        // 添加智能验证和修正
                        const enableSmartCorrection = document.getElementById('enableSmartCorrection')?.checked !== false;
                        const showCorrectionNotes = document.getElementById('showCorrectionNotes')?.checked !== false;
                        const strictVerseValidation = document.getElementById('strictVerseValidation')?.checked !== false;

                        // 先对项目进行校验和修正，再去重
                        const correctedItems = [];
                        for (const item of foundItems) {
                            // 创建一个新项目进行潜在修正
                            const correctedItem = { ...item, correctionNote: null, isInvalid: false };

                            if (enableSmartCorrection) {
                                // 1. 检查书卷是否有效
                                if (!bookChapterLimits[correctedItem.book]) {
                                    // 尝试找到最接近的书卷名
                                    const bookNames = Object.keys(bookChapterLimits);
                                    const similarBook = findMostSimilarString(correctedItem.book, bookNames);
                                    if (similarBook) {
                                        correctedItem.correctionNote = `原书卷"${correctedItem.book}"已更正为"${similarBook}"`;
                                        correctedItem.book = similarBook;
                                        // 更新引用文本
                                        correctedItem.ref = correctedItem.ref.replace(/^[^ ]+/, similarBook);
                                    }
                                }

                                // 2. 检查章节是否在范围内
                                const maxChapter = bookChapterLimits[correctedItem.book] || 150; // 使用150作为最大值的保守估计
                                if (correctedItem.chapter > maxChapter) {
                                    if (maxChapter === 1) {
                                        // 对于单章书卷，把章号当作节号处理
                                        correctedItem.verse = correctedItem.chapter;
                                        correctedItem.chapter = 1;
                                        correctedItem.correctionNote = (correctedItem.correctionNote ? correctedItem.correctionNote + "; " : "") +
                                            `${correctedItem.book}为单章书卷，已将章号"${correctedItem.chapter}"改为节号`;
                                        // 更新引用文本
                                        correctedItem.ref = ReferenceFormatManager.formatReference(correctedItem.book, 1, correctedItem.verse) + (item.ref.includes('下') ? '下' : '');
                                    } else {
                                        // 如果章号超出范围，尝试使用最大章号
                                        correctedItem.correctionNote = (correctedItem.correctionNote ? correctedItem.correctionNote + "; " : "") +
                                            `章号超出范围(最大${maxChapter}章)，已更正为最大章号，如果不对，请手动修改`;
                                        correctedItem.chapter = maxChapter;
                                        // 更新引用文本
                                        const parts = correctedItem.ref.split(':');
                                        if (parts.length > 1) {
                                            parts[0] = `${correctedItem.book} ${maxChapter}`;
                                            correctedItem.ref = parts.join(':');
                                        }
                                    }
                                }

                                // 3. 如果启用了严格验证且有JSON数据，则验证节号
                                if (strictVerseValidation && bibleDataToUse) {
                                    // 获取该章的最大节数
                                    let maxVerse = 0;
                                    // 构建一个表达式来匹配此书此章的所有节
                                    const verseRegex = new RegExp(`^${correctedItem.book} ${correctedItem.chapter}:(\\d+)$`);

                                    // 如果verseCountData中已有该书章的数据，直接使用
                                    const bookChapterKey = `${correctedItem.book}_${correctedItem.chapter}`;
                                    if (DataManager.verseCountData[bookChapterKey]) {
                                        maxVerse = DataManager.verseCountData[bookChapterKey];
                                    } else {
                                        // 否则计算该章的最大节数
                                        for (const key in bibleDataToUse) {
                                            if (key.startsWith(`${correctedItem.book} ${correctedItem.chapter}:`)) {
                                                const match = key.match(verseRegex);
                                                if (match) {
                                                    const verseNum = parseInt(match[1], 10);
                                                    maxVerse = Math.max(maxVerse, verseNum);
                                                }
                                            }
                                        }
                                        // 保存到缓存
                                        DataManager.verseCountData[bookChapterKey] = maxVerse;
                                    }

                                    // 验证节号
                                    if (maxVerse > 0 && correctedItem.verse > maxVerse) {
                                        if (correctedItem.verse <= maxVerse + 5) {
                                            // 如果节号仅略微超出范围，尝试修正为最大节号
                                            correctedItem.correctionNote = (correctedItem.correctionNote ? correctedItem.correctionNote + "; " : "") +
                                                `节号超出范围(最大${maxVerse}节)，已更正`;
                                            correctedItem.verse = maxVerse;
                                            // 更新引用文本
                                            correctedItem.ref = ReferenceFormatManager.formatReference(correctedItem.book, correctedItem.chapter, maxVerse) + (item.ref.includes('下') ? '下' : '');
                                            // 更新文本键以重新查找经文
                                            correctedItem.text = bibleData[`${correctedItem.book} ${correctedItem.chapter}:${maxVerse}`] || null;
                                        } else {
                                            // 如果节号超出范围太多，标记为无效但不修改
                                            correctedItem.isInvalid = true;
                                            correctedItem.correctionNote = `节号${correctedItem.verse}明显超出范围(最大${maxVerse}节)`;
                                        }
                                    }
                                }
                            }

                            correctedItems.push(correctedItem);
                        }

                        // 处理修正后的项目并去重
                        const uniqueOutputItems = [];
                        const seenRefs = new Set();
                        for (const item of correctedItems) {
                            if (!seenRefs.has(item.ref)) {
                                uniqueOutputItems.push(item);
                                seenRefs.add(item.ref);
                            }
                        }

                        uniqueOutputItems.forEach(item => {
                            // 悬浮预览引用
                            if (item.correctionNote && showCorrectionNotes) {
                                // 如果有修正且启用显示修正提示，将修正注释添加到悬停预览中
                                processedHtml += `<span class="reference-hover auto-corrected">${escapeHtml(item.ref)}`;
                                processedHtml += `<span class="correction-note">${escapeHtml(item.correctionNote)}</span>`;
                            } else {
                                processedHtml += `<span class="reference-hover">${escapeHtml(item.ref)}`;
                            }

                            processedHtml += `<span class="verse-preview">`;
                            processedHtml += `<div class="verse-preview-header">${escapeHtml(item.ref)}</div>`;

                            if (item.correctionNote && showCorrectionNotes) {
                                processedHtml += `<div style="color:#856404;margin-bottom:5px;font-size:0.9em;background:#fff8e5;padding:3px;border-radius:3px;">修正: ${escapeHtml(item.correctionNote)}</div>`;
                            }

                            if (item.isInvalid) {
                                processedHtml += `<div class="verse-preview-not-found">无效引用: ${escapeHtml(item.correctionNote)}</div>`;
                            } else if (item.text) {
                                processedHtml += `<div class="verse-preview-text">${escapeHtml(item.text)}</div>`;
                            } else {
                                // 检查是否是因为章节超出范围导致找不到经文
                                if (item.correctionNote && (item.correctionNote.includes('章号超出范围') || item.correctionNote.includes('节号超出范围'))) {
                                    processedHtml += `<div class="verse-preview-not-found">无效引用: ${escapeHtml(item.correctionNote)}</div>`;
                                } else {
                                    processedHtml += `<div class="verse-preview-not-found">经文未在JSON中找到</div>`;
                                }
                            }

                            processedHtml += `</span>`;
                            processedHtml += `</span>`;

                            // 总是显示格式化的引用
                            if (item.isInvalid) {
                                // 对于无效引用，显示特殊样式
                                processedHtml += `<span class="scripture-invalid">\t${escapeHtml(item.correctionNote || '无效引用')}</span>\n`;
                            } else if (item.text) {
                                processedHtml += `<span class="scripture-text">\t${escapeHtml(item.text)}</span>\n`;
                            } else {
                                // 检查是否是因为章节超出范围导致找不到经文
                                if (item.correctionNote && (item.correctionNote.includes('章号超出范围') || item.correctionNote.includes('节号超出范围'))) {
                                    processedHtml += `<span class="scripture-invalid">\t${escapeHtml(item.correctionNote)}</span>\n`;
                                } else {
                                    processedHtml += `<span class="scripture-not-found">  -> (经文未在JSON中找到)</span>\n`;
                                }
                            }
                        });
                        processedHtml += `</div>`; // 关闭 processed-references div
                    }

                    processedHtml += `</div>`; // 关闭 line-group div
                }
                return processedHtml;
            }, '格式化文本查找经文', '<div class="line-group"><div class="scripture-not-found">查找经文时出错，请重试。</div></div>');
        }

        // --- 文本框输入事件监听器（支持异步处理） ---
        // "一步完成"标签页的输入框
        document.getElementById('originalContent').addEventListener('input', async (event) => {
            const text = event.target.value;
            const outputElement = document.getElementById('output');

            if (DataManager.isDataLoaded()) { // 如果JSON已加载
                try {
                    // 检测大文本
                    const detection = LargeTextDetector.detect(text);

                    if (detection.isLarge && LargeTextDetector.config.showWarning) {
                        // 显示大文本警告
                        const showWarning = LargeTextDetector.showWarning(detection);
                        if (showWarning) {
                            // 等待用户选择
                            return;
                        }
                    }

                    // 显示处理中状态
                    if (detection.isLarge) {
                        outputElement.innerHTML = '<div class="line-group"><div class="scripture-not-found">正在处理大文本，请稍候...</div></div>';
                    }

                    const result = await processText(text, true); // 异步处理文本并查找经文
                    outputElement.innerHTML = result;

                    // 根据按钮的当前状态重新应用可见性
                    if (applyOutputVisibility) applyOutputVisibility(document.getElementById('toggleOutputOriginalButton').dataset.state === 'visible');

                } catch (error) {
                    outputElement.innerHTML = `<div class="line-group"><div class="scripture-not-found">处理文本时出错: ${escapeHtml(error.message)}<br><pre>${escapeHtml(error.stack)}</pre></div></div>`;
                }
            } else { // 如果JSON未加载
                if (text.trim() === '') {
                    outputElement.innerHTML = '结果将显示在这里...';
                } else {
                    outputElement.innerHTML = `<div class="line-group"><div class="scripture-not-found">请先加载JSON文件才能进行处理。</div></div>`;
                }
                // 如果JSON未加载，通常默认显示原文或者显示提示信息
                if (applyOutputVisibility) applyOutputVisibility(true);
            }
        });

        // "格式化引用"标签页的输入框
        document.getElementById('formatOriginal').addEventListener('input', async (event) => {
            try {
                const text = event.target.value;
                const detection = LargeTextDetector.detect(text);

                if (detection.isLarge) {
                    document.getElementById('formatOutput').innerHTML = '<div class="line-group"><div class="scripture-not-found">正在处理大文本，请稍候...</div></div>';
                }

                const result = await processText(text, false); // 🔧 修改：只格式化引用，转换为简称，不查找经文
                document.getElementById('formatOutput').innerHTML = result;
                if (applyFormatOutputVisibility) applyFormatOutputVisibility(document.getElementById('toggleFormatOutputOriginalButton').dataset.state === 'visible');
            } catch (error) {
                document.getElementById('formatOutput').innerHTML = `<div class="line-group"><div class="scripture-not-found">处理文本时出错: ${escapeHtml(error.message)}<br><pre>${escapeHtml(error.stack)}</pre></div></div>`;
            }
        });

        // "查找经文"标签页的输入框 - *** 更改为调用新的 lookupVersesFromFormattedText 函数 ***
        document.getElementById('verseOriginal').addEventListener('input', (event) => {
            const text = event.target.value;
            const outputElement = document.getElementById('verseOutput');
            if (DataManager.isDataLoaded()) { // 如果JSON已加载
                try {
                    const result = lookupVersesFromFormattedText(text, DataManager.getBibleData()); // 调用新函数
                    outputElement.innerHTML = result;
                    if (applyVerseOutputVisibility) applyVerseOutputVisibility(document.getElementById('toggleVerseOutputOriginalButton').dataset.state === 'visible');
                } catch (error) {
                    outputElement.innerHTML = `<div class="line-group"><div class="scripture-not-found">处理文本时出错: ${escapeHtml(error.message)}<br><pre>${escapeHtml(error.stack)}</pre></div></div>`;
                }
            } else { // 如果JSON未加载
                if (text.trim() === '') {
                    outputElement.innerHTML = '经文将显示在这里...';
                } else {
                    outputElement.innerHTML = `<div class="line-group"><div class="scripture-not-found">请先加载JSON文件才能进行处理。</div></div>`;
                }
                if (applyVerseOutputVisibility) applyVerseOutputVisibility(true);
            }
        });

        // --- 文件处理事件监听器 ---

        // "一步完成"标签页的JSON文件输入
        jsonFileInput.addEventListener('change', (event) => {
            FileManager.handleJsonFileLoad(event.target.files[0], () => {
                // JSON加载（或失败）后，触发原始文本框的input事件
                originalContent.dispatchEvent(new Event('input'));
            });
        });

        // "查找经文"标签页的JSON文件输入
        verseJsonInput.addEventListener('change', (event) => {
            FileManager.handleJsonFileLoad(event.target.files[0], () => {
                verseOriginal.dispatchEvent(new Event('input'));
            });
        });

        // "悬停阅读"标签页的JSON文件输入
        hoverJsonInput.addEventListener('change', (event) => {
            FileManager.handleJsonFileLoad(event.target.files[0], () => {
                // 如果已有文本内容，重新处理
                const hoverContent = document.getElementById('hoverContent');
                if (hoverContent.textContent.trim() !== '请先加载JSON，然后选择TXT文件或直接在此输入内容...') {
                    processHoverReadContent(hoverContent.textContent);
                }
            });
        });

        // 处理TXT文件加载的通用函数
        function handleTxtFileLoad(file, originalContentElementId) {
            const originalContentEl = document.getElementById(originalContentElementId);
            if (!file) {
                originalContentEl.value = '';
                originalContentEl.dispatchEvent(new Event('input'));
                return;
            }

            FileManager.handleTextFileLoad(file, (content) => {
                originalContentEl.value = content;
                originalContentEl.dispatchEvent(new Event('input'));
            });
        }

        // "一步完成"标签页的TXT文件输入 - 支持多文件选择
        txtFileInput.addEventListener('change', (event) => {
            const files = event.target.files;
            if (!files || files.length === 0) return;

            // 如果只选择了一个文件，按原来的逻辑处理
            if (files.length === 1) {
                handleTxtFileLoad(files[0], 'originalContent');
                document.getElementById('batchFilesSection').style.display = 'none';
                return;
            }

            // 多文件处理逻辑
            batchFiles = Array.from(files);
            currentBatchIndex = 0;
            batchResults = [];

            // 显示批量处理区域
            const batchFilesSection = document.getElementById('batchFilesSection');
            batchFilesSection.style.display = 'block';

            // 显示文件列表
            const batchFilesList = document.getElementById('batchFilesList');
            batchFilesList.innerHTML = '';
            batchFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.textContent = `${index + 1}. ${file.name} (${(file.size / 1024).toFixed(2)} KB)`;
                batchFilesList.appendChild(fileItem);
            });

            // 更新状态
            document.getElementById('batchStatus').textContent = `已选择 ${batchFiles.length} 个文件`;
        });

        // "格式化引用"标签页的TXT文件输入
        formatTxtInput.addEventListener('change', (event) => {
            handleTxtFileLoad(event.target.files[0], 'formatOriginal');
        });

        // "查找经文"标签页的TXT文件输入 (通常是已格式化的引用文件)
        verseTxtInput.addEventListener('change', (event) => {
            handleTxtFileLoad(event.target.files[0], 'verseOriginal');
        });


        // --- 复制和下载功能 ---

        // 设置复制按钮功能的通用函数
        function setupCopyButton(buttonId, sourceElementId) {
            const button = document.getElementById(buttonId);
            if (!button) return;
            button.addEventListener('click', async () => {
                try {
                    const sourceElement = document.getElementById(sourceElementId);

                    // 使用htmlToText函数获取纯文本
                    const textToCopy = htmlToText(sourceElement.innerHTML);

                    await navigator.clipboard.writeText(textToCopy);
                    const originalText = button.textContent;
                    button.textContent = '复制成功!';
                    button.classList.add('success'); // 改变按钮外观提示成功
                    setTimeout(() => { // 2秒后恢复按钮原状
                        button.textContent = originalText;
                        button.classList.remove('success');
                    }, 2000);
                } catch (err) {
                    console.error('复制失败:', err);
                    const originalText = button.textContent;
                    button.textContent = '复制失败';
                    button.classList.add('success'); // 改变按钮外观提示失败 (使用和成功一样的类，颜色可调)
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.classList.remove('success');
                    }, 2000);
                }
            });
        }

        // 为三个复制按钮分别设置功能
        setupCopyButton('copyButton', 'output');
        setupCopyButton('copyFormatButton', 'formatOutput');
        setupCopyButton('copyVerseButton', 'verseOutput');

        // "格式化引用"标签页的下载按钮功能
        document.getElementById('downloadFormatButton').addEventListener('click', () => {
            const formatOutputElement = document.getElementById('formatOutput');

            // 使用htmlToText函数获取纯文本
            const textToDownload = htmlToText(formatOutputElement.innerHTML);

            const blob = new Blob([textToDownload], { type: 'text/plain;charset=utf-8' }); // 创建文本Blob对象
            const url = window.URL.createObjectURL(blob); // 创建Blob URL
            const a = document.createElement('a'); // 创建隐藏的下载链接
            a.href = url;
            a.download = '格式化结果.txt'; // 设置下载文件名
            document.body.appendChild(a);
            a.click(); // 触发下载
            window.URL.revokeObjectURL(url); // 释放Blob URL
            document.body.removeChild(a); // 移除下载链接
        });


        // --- 新增：切换原文显示功能 ---
        let applyOutputVisibility;        // 用于"一步完成"输出区域的可见性控制函数
        let applyFormatOutputVisibility;  // 用于"格式化引用"输出区域的可见性控制函数
        let applyVerseOutputVisibility;   // 用于"查找经文"输出区域的可见性控制函数

        // 通用函数：设置切换按钮的行为
        function setupToggleOriginalButton(buttonId, outputElementId) {
            const button = document.getElementById(buttonId);         // 获取切换按钮
            const outputElement = document.getElementById(outputElementId); // 获取对应的输出区域元素

            if (!button || !outputElement) return; // 如果元素不存在，则退出

            // 保持一个内部状态，表示原文是否可见 (默认为可见)
            let isOriginalVisible = true;

            // 根据传入的 visible 状态，隐藏或显示所有原始行
            const applyVisibility = (visible) => {
                isOriginalVisible = visible; // 更新内部状态
                // 获取输出区域内所有带有 'original-line-wrapper' 类的元素
                const originalLineWrappers = outputElement.querySelectorAll('.original-line-wrapper');
                originalLineWrappers.forEach(wrapper => {
                    if (visible) { // 如果要显示
                        wrapper.classList.remove('hidden'); // 移除 'hidden' 类
                    } else { // 如果要隐藏
                        wrapper.classList.add('hidden');    // 添加 'hidden' 类
                    }
                });
                // 更新按钮的文本和 data-state 属性以反映当前状态
                button.textContent = visible ? '隐藏原文' : '显示原文';
                button.dataset.state = visible ? 'visible' : 'hidden';
            };

            // 页面加载时，初始应用可见性 (默认为可见)
            applyVisibility(isOriginalVisible);

            // 为按钮添加点击事件监听器
            button.addEventListener('click', () => {
                applyVisibility(!isOriginalVisible); // 点击时切换可见状态
            });

            return applyVisibility; // 返回这个函数，以便在内容更新后可以重新应用可见性
        }

        // 页面内容加载完毕后，初始化所有的切换按钮和自动加载圣经数据
        document.addEventListener('DOMContentLoaded', () => {
            // 🔧 新增：初始化引用格式配置管理器
            ReferenceFormatManager.init();
            console.log('✅ 引用格式配置管理器初始化完成');

            applyOutputVisibility = setupToggleOriginalButton('toggleOutputOriginalButton', 'output');
            applyFormatOutputVisibility = setupToggleOriginalButton('toggleFormatOutputOriginalButton', 'formatOutput');
            applyVerseOutputVisibility = setupToggleOriginalButton('toggleVerseOutputOriginalButton', 'verseOutput');

            // 自动加载圣经数据
            autoLoadBibJson();

            // 为移动设备添加全局触摸支持（所有标签页中的引用悬停预览）
            document.body.addEventListener('touchstart', function (e) {
                // 检查是否点击了引用元素
                let target = e.target;
                while (target != null) {
                    if (target.classList && target.classList.contains('reference-hover')) {
                        // 找到所有已打开的预览并关闭
                        document.querySelectorAll('.verse-preview.touch-active').forEach(el => {
                            if (el !== target.querySelector('.verse-preview')) {
                                el.classList.remove('touch-active');
                            }
                        });

                        // 切换当前预览的显示状态
                        const preview = target.querySelector('.verse-preview');
                        if (preview) {
                            preview.classList.toggle('touch-active');
                            e.preventDefault(); // 防止其他点击事件
                        }
                        break;
                    }
                    target = target.parentElement;
                }
            });

            // 添加CSS规则，支持所有标签页中触摸设备上的预览显示
            const style = document.createElement('style');
            style.textContent = `
                @media (hover: none) {
                    .reference-hover:hover .verse-preview {
                        display: none; /* 在触摸设备上禁用hover */
                    }
                    .verse-preview.touch-active {
                        display: block !important; /* 在触摸设备上通过类控制显示 */
                        position: fixed !important; /* 固定定位，防止滚动时消失 */
                        bottom: auto !important; /* 取消底部定位 */
                        top: 50% !important; /* 居中显示 */
                        left: 50% !important;
                        transform: translate(-50%, -50%) !important;
                        width: 90% !important;
                        max-width: 350px !important;
                        max-height: 80% !important;
                        z-index: 1000 !important;
                        box-shadow: 0 0 0 9999px rgba(0,0,0,0.5) !important; /* 添加背景遮罩 */
                    }
                    .verse-preview.touch-active::after {
                        content: '点击关闭';
                        display: block;
                        text-align: center;
                        margin-top: 10px;
                        padding-top: 5px;
                        border-top: 1px solid #eee;
                        color: #666;
                        font-style: italic;
                        font-size: 0.9em;
                    }
                }
            `;
            document.head.appendChild(style);
        });

        // 批量处理相关变量
        let batchFiles = [];
        let currentBatchIndex = 0;
        let batchResults = [];

        // 批量处理按钮事件（增强版）
        document.getElementById('processBatchButton').addEventListener('click', async () => {
            if (!DataManager.isDataLoaded()) {
                DataManager.showUserFriendlyError(
                    new Error('请先加载经文JSON文件'),
                    '批量处理'
                );
                return;
            }

            if (batchFiles.length === 0) {
                alert('请先选择要处理的文件！');
                return;
            }

            // 禁用按钮，防止重复点击
            const processButton = document.getElementById('processBatchButton');
            const progressBar = document.getElementById('batchProgress');
            const progressFill = document.getElementById('batchProgressFill');
            const progressText = document.getElementById('batchProgressText');

            processButton.disabled = true;
            processButton.textContent = '处理中...';
            progressBar.style.display = 'block';

            // 清空结果数组
            batchResults = [];

            try {
                // 逐个处理文件
                for (let i = 0; i < batchFiles.length; i++) {
                    const file = batchFiles[i];
                    const progress = ((i + 1) / batchFiles.length) * 100;

                    // 更新进度
                    progressFill.style.width = `${progress}%`;
                    progressText.textContent = `正在处理: ${i + 1}/${batchFiles.length} - ${file.name}`;
                    document.getElementById('batchStatus').textContent = `处理进度: ${Math.round(progress)}%`;

                    try {
                        // 读取文件内容
                        const content = await readFileAsync(file);

                        // 处理文本
                        const result = processText(content, true);

                        // 将HTML结果转换为纯文本
                        const textResult = htmlToText(result);

                        // 保存结果
                        batchResults.push({
                            fileName: file.name,
                            content: content,
                            result: textResult,
                            success: true
                        });
                    } catch (error) {
                        console.error(`处理文件 ${file.name} 时出错:`, error);
                        batchResults.push({
                            fileName: file.name,
                            content: '',
                            result: `处理失败: ${error.message}`,
                            success: false
                        });
                    }

                    // 添加小延迟，让用户看到进度变化
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            } catch (error) {
                DataManager.showUserFriendlyError(error, '批量处理');
                return;
            }

            // 创建ZIP文件
            progressText.textContent = '正在生成下载文件...';
            progressFill.style.width = '100%';

            const zip = new JSZip();
            const successCount = batchResults.filter(item => item.success).length;
            const failCount = batchResults.length - successCount;

            batchResults.forEach(item => {
                const resultFileName = item.fileName.replace('.txt', '') + '_processed.txt';
                zip.file(resultFileName, item.result);
            });

            // 添加处理报告
            const reportContent = `批量处理报告
处理时间: ${new Date().toLocaleString()}
总文件数: ${batchResults.length}
成功处理: ${successCount}
处理失败: ${failCount}

${failCount > 0 ? '失败文件列表:\n' + batchResults.filter(item => !item.success).map(item => `- ${item.fileName}: ${item.result}`).join('\n') : ''}
`;
            zip.file('处理报告.txt', reportContent);

            // 生成ZIP文件并下载
            zip.generateAsync({ type: 'blob' }).then(function (content) {
                const url = window.URL.createObjectURL(content);
                const a = document.createElement('a');
                a.href = url;
                a.download = '批量处理结果.zip';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // 恢复按钮状态
                processButton.disabled = false;
                processButton.textContent = '开始批量处理';
                progressBar.style.display = 'none';

                // 显示完成状态
                const statusMessage = failCount > 0
                    ? `已完成 ${batchFiles.length} 个文件的处理 (${successCount} 成功, ${failCount} 失败)`
                    : `已成功完成 ${batchFiles.length} 个文件的处理`;

                document.getElementById('batchStatus').innerHTML = `
                    <span style="color: ${failCount > 0 ? '#856404' : '#28a745'};">
                        ✓ ${statusMessage}
                    </span>
                `;
            }).catch(error => {
                DataManager.showUserFriendlyError(error, 'ZIP文件生成');
                processButton.disabled = false;
                processButton.textContent = '开始批量处理';
                progressBar.style.display = 'none';
            });
        });

        // 辅助函数：异步读取文件内容
        function readFileAsync(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = (e) => reject(e);
                reader.readAsText(file, 'UTF-8');
            });
        }

        // 辅助函数：将HTML结果转换为纯文本
        function htmlToText(html) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            let result = '';
            const lineGroups = tempDiv.querySelectorAll('.line-group');

            lineGroups.forEach(group => {
                const originalLine = group.querySelector('.original-line-wrapper');
                const processedRefs = group.querySelector('.processed-references');
                const topLevelErrorSpan = group.querySelector(':scope > .scripture-not-found');

                // 如果有顶层错误信息且原始行隐藏，只复制错误
                if (topLevelErrorSpan && (!originalLine || originalLine.classList.contains('hidden'))) {
                    result += topLevelErrorSpan.textContent.trim() + '\n';
                    return;
                }

                // 复制原始行（如果可见）
                if (originalLine && !originalLine.classList.contains('hidden')) {
                    result += originalLine.textContent.trim() + '\n';
                }

                // 处理引用和经文
                if (processedRefs) {
                    // 找到所有引用，但不包括悬浮预览部分
                    const referenceHovers = processedRefs.querySelectorAll('.reference-hover');

                    if (referenceHovers.length > 0) {
                        referenceHovers.forEach(hover => {
                            // 创建一个引用的副本
                            const clonedHover = hover.cloneNode(true);

                            // 移除悬浮预览元素
                            const previewToRemove = clonedHover.querySelector('.verse-preview');
                            if (previewToRemove) {
                                clonedHover.removeChild(previewToRemove);
                            }

                            // 添加引用文本
                            result += clonedHover.textContent.trim();

                            // 找到紧跟在引用后面的经文文本元素
                            const nextSibling = hover.nextElementSibling;
                            if (nextSibling && (nextSibling.classList.contains('scripture-text') ||
                                nextSibling.classList.contains('scripture-not-found') ||
                                nextSibling.classList.contains('scripture-invalid'))) {
                                result += nextSibling.textContent;
                            }

                            result += '\n';
                        });
                    } else {
                        // 如果没有引用悬停元素，则处理纯文本内容
                        result += processedRefs.textContent.trim() + '\n';
                    }
                }
            });

            return result;
        }

        // 在页面加载时添加JSZip库并初始化所有功能
        document.addEventListener('DOMContentLoaded', () => {
            // 添加JSZip库
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            script.integrity = 'sha512-XMVd28F1oH/O71fzwBnV7HucLxVwtxf26XV8P4wPk26EDxuGZ91N8bsOttmnomcCD3CS5ZMRL50H0GgOHvegtg==';
            script.crossOrigin = 'anonymous';
            document.head.appendChild(script);

            // 设置切换按钮
            applyOutputVisibility = setupToggleOriginalButton('toggleOutputOriginalButton', 'output');
            applyFormatOutputVisibility = setupToggleOriginalButton('toggleFormatOutputOriginalButton', 'formatOutput');
            applyVerseOutputVisibility = setupToggleOriginalButton('toggleVerseOutputOriginalButton', 'verseOutput');

            // 为移动设备添加触摸支持（引用悬停预览）
            document.body.addEventListener('touchstart', function (e) {
                // 检查是否点击了引用元素
                let target = e.target;
                while (target != null) {
                    if (target.classList && target.classList.contains('reference-hover')) {
                        // 找到所有已打开的预览并关闭
                        document.querySelectorAll('.verse-preview.touch-active').forEach(el => {
                            if (el !== target.querySelector('.verse-preview')) {
                                el.classList.remove('touch-active');
                            }
                        });

                        // 切换当前预览的显示状态
                        const preview = target.querySelector('.verse-preview');
                        if (preview) {
                            preview.classList.toggle('touch-active');
                            e.preventDefault(); // 防止其他点击事件
                        }
                        break;
                    }
                    target = target.parentElement;
                }
            });

            // 添加CSS规则，支持触摸设备上的预览显示
            const style = document.createElement('style');
            style.textContent = `
                @media (hover: none) {
                    .reference-hover:hover .verse-preview {
                        display: none; /* 在触摸设备上禁用hover */
                    }
                    .verse-preview.touch-active {
                        display: block !important; /* 在触摸设备上通过类控制显示 */
                        position: fixed !important; /* 固定定位，防止滚动时消失 */
                        bottom: auto !important; /* 取消底部定位 */
                        top: 50% !important; /* 居中显示 */
                        left: 50% !important;
                        transform: translate(-50%, -50%) !important;
                        width: 90% !important;
                        max-width: 350px !important;
                        max-height: 80% !important;
                        z-index: 1000 !important;
                        box-shadow: 0 0 0 9999px rgba(0,0,0,0.5) !important; /* 添加背景遮罩 */
                    }
                    .verse-preview.touch-active::after {
                        content: '点击关闭';
                        display: block;
                        text-align: center;
                        margin-top: 10px;
                        padding-top: 5px;
                        border-top: 1px solid #eee;
                        color: #666;
                        font-style: italic;
                        font-size: 0.9em;
                    }
                }
            `;
            document.head.appendChild(style);

            // 自动加载圣经数据
            autoLoadBibJson();
        });

        // --- 自动加载圣经数据 ---
        function autoLoadBibJson() {
            // 检查BibleData是否已经加载
            if (typeof BibleData !== 'undefined' && BibleData) {
                console.log('📖 使用内嵌的圣经数据');
                DataManager.loadBibleData(BibleData);
                DataManager.cacheData(BibleData);
                DataManager.updateAllStatusElements('内嵌数据加载成功', '#28a745', 'success');
                return;
            }

            // 如果内嵌数据不可用，尝试从缓存加载
            if (DataManager.loadFromCache()) {
                return;
            }

            // 如果都失败了，显示错误信息
            console.error('❌ 圣经数据不可用：内嵌数据和缓存都失败');
            DataManager.updateAllStatusElements('数据加载失败：请检查bib-data.js文件', '#dc3545', 'error');
        }

        // 在文件末尾添加字符串相似度计算函数
        // 添加辅助函数：寻找最相似的字符串
        function findMostSimilarString(target, candidates) {
            if (!target || candidates.length === 0) return null;

            // 使用缓存避免重复计算
            if (!findMostSimilarString.cache) {
                findMostSimilarString.cache = new Map();
            }

            const cacheKey = target + '|' + candidates.join(',');
            if (findMostSimilarString.cache.has(cacheKey)) {
                return findMostSimilarString.cache.get(cacheKey);
            }

            let bestMatch = null;
            let highestSimilarity = 0;

            for (const candidate of candidates) {
                const similarity = calculateStringSimilarity(target, candidate);
                if (similarity > highestSimilarity) {
                    highestSimilarity = similarity;
                    bestMatch = candidate;
                }
            }

            // 只有当相似度高于阈值时才返回最佳匹配
            const result = highestSimilarity > 0.6 ? bestMatch : null;
            findMostSimilarString.cache.set(cacheKey, result);
            return result;
        }

        // 计算两个字符串的相似度（0-1之间）- 优化版本
        function calculateStringSimilarity(str1, str2) {
            // 快速路径
            if (str1 === str2) return 1.0;
            if (str1.length === 0 || str2.length === 0) return 0.0;

            // 缓存机制
            if (!calculateStringSimilarity.cache) {
                calculateStringSimilarity.cache = new Map();
            }

            // 创建缓存键（确保顺序一致）
            const cacheKey = str1 < str2 ? str1 + '|' + str2 : str2 + '|' + str1;
            if (calculateStringSimilarity.cache.has(cacheKey)) {
                return calculateStringSimilarity.cache.get(cacheKey);
            }

            // 优化：对于长度差异很大的字符串，可以快速返回较低的相似度
            const lenDiff = Math.abs(str1.length - str2.length);
            const maxLen = Math.max(str1.length, str2.length);
            if (lenDiff > maxLen * 0.7) {
                return 0.3; // 返回一个较低的相似度值
            }

            // 使用Levenshtein距离算法
            const matrix = [];

            // 初始化矩阵
            for (let i = 0; i <= str1.length; i++) {
                matrix[i] = [i];
            }
            for (let j = 0; j <= str2.length; j++) {
                matrix[0][j] = j;
            }

            // 填充矩阵
            for (let i = 1; i <= str1.length; i++) {
                for (let j = 1; j <= str2.length; j++) {
                    const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j] + 1,      // 删除
                        matrix[i][j - 1] + 1,      // 插入
                        matrix[i - 1][j - 1] + cost  // 替换
                    );
                }
            }

            // 计算归一化相似度（1 - 归一化距离）
            const similarity = 1 - matrix[str1.length][str2.length] / maxLen;
            calculateStringSimilarity.cache.set(cacheKey, similarity);
            return similarity;
        }

        // --- 悬停阅读功能 ---
        // 处理"悬停阅读"标签页的TXT文件加载
        document.getElementById('hoverTxtInput').addEventListener('change', (event) => {
            handleTxtFileLoad(event.target.files[0], 'hoverOriginal');
        });

        // 处理"悬停阅读"内容
        function processHoverReadContent(text) {
            const hoverContentElement = document.getElementById('hoverContent');

            if (!DataManager.isDataLoaded()) {
                hoverContentElement.innerHTML = '<div style="color: #721c24; background-color: #f8d7da; border-left: 3px solid #dc3545; padding: 10px; margin: 10px 0;">请先加载经文 JSON 文件。</div>';
                return;
            }

            const hoverReadBibleData = DataManager.getBibleData();

            try {
                // 处理文本，识别经文引用并添加悬停功能
                hoverContentElement.innerHTML = "正在处理文本，请稍候...";

                // 使用优化的正则表达式工厂
                const bookAbbrRegexPart = RegexFactory.createBookRegexPart(bookMap);
                const fullBookNamesRegexPart = RegexFactory.createBookRegexPart(fullBookNameToAbbrMap);

                // 定义中文数字和阿拉伯数字的正则模式
                const cnNum = REGEX.CN_NUM; // 使用预定义的中文数字正则
                const cnVerseAndRange = REGEX.CN_VERSE_RANGE; // 使用预定义的中文节号或范围
                const arNumAndRange = REGEX.AR_NUM_RANGE; // 使用预定义的阿拉伯数字节号或范围

                // 构建核心的引用匹配正则表达式 (匹配多种格式) - 与一步完成功能使用相同的正则
                const refRegex = new RegExp(
                    // 格式1: (参)书名(全/简)+中文章号+章+中文节号(范围)+(节)+(下)
                    `(?:参)?(${fullBookNamesRegexPart}|${bookAbbrRegexPart})(${cnNum})章(${cnVerseAndRange})(?:节)?(下)?` +
                    `|` +
                    // 格式2: (参)书名(简)+中文章号+阿拉伯节号(范围)+(节)+(下)
                    `(?:参)?(${bookAbbrRegexPart})(${cnNum})(${arNumAndRange})(?:节)?(下)?` +
                    `|` +
                    // 格式3: 中文章号+章+中文节号(范围)+(节)+(下) (无书名，依赖上下文)
                    `(${cnNum})章(${cnVerseAndRange})(?:节)?(下)?` +
                    `|` +
                    // 格式4: 中文章号+阿拉伯节号(范围)+(节)+(下) (无书名，依赖上下文)
                    `(${cnNum})(${arNumAndRange})(?:节)?(下)?` +
                    `|` +
                    // 格式5: (参)书名(简)+阿拉伯章号[:：]?阿拉伯节号(范围)+(节)+(下) (单章书卷也适用)
                    `(?:参)?(${bookAbbrRegexPart})(\\d+[:：]?${arNumAndRange})(?:节)?(下)?` +
                    `|` +
                    // 新增格式：单章书卷+节号(无章号) - 例如"门 1" 或 "犹 5-7"
                    `(?:参)?(${bookAbbrRegexPart})\\s+(${arNumAndRange})(?:节)?(下)?` +
                    `|` +
                    // 新增格式：单章书卷+节号(无空格) - 例如"门1" 或 "犹5-7"
                    `(?:参)?(${bookAbbrRegexPart})(${arNumAndRange})(?:节)?(下)?` +
                    `|` +
                    // 新增格式：书名(全名)+中文节号（无章号，适用于单章书卷）- 如"犹大书一节"
                    `(?:参)?(${fullBookNamesRegexPart})(${cnVerseAndRange})(?:节)?(下)?` +
                    `|` +
                    // 🔧 新增格式：跨章引用 - 如"三章一节至四章三十一节"
                    `(${cnNum})章(${cnVerseAndRange})(?:节)?(?:至|到|-|~)(${cnNum})章(${cnVerseAndRange})(?:节)?(下)?` +
                    `|` +
                    // 🔧 新增格式9：仅中文节号范围（依赖上下文）- 如"十五至十七节"
                    `(${cnVerseAndRange})(?:节)?(下)?` +
                    `|` +
                    // 格式10: 阿拉伯节号(范围)+(节)+(下) (无书名和章号，强依赖行内上下文)
                    `(${arNumAndRange})(?:节)?(下)?`,
                    'g' // 全局匹配
                );

                // 用于在行内查找书卷和章上下文的正则表达式 (例如 "约翰福音三章")
                const inlineContextRegex = new RegExp(
                    `(${fullBookNamesRegexPart}|${bookAbbrRegexPart})` + // 书名 (全/简)
                    `([一二三四五六七八九十百千〇零]+)章`, // 中文章号 + "章"
                    'g' // 全局匹配
                );

                // 处理每一行文本
                let processedText = '';
                const lines = text.split('\n');

                // lastBook 和 lastChapter 在每次调用 processHoverReadContent 时重新初始化，以确保上下文独立
                let lastBook = null; // 上一个成功解析的书卷 (用于跨行或行内较早的引用)
                let lastChapter = null; // 上一个成功解析的章号 (用于跨行或行内较早的引用)

                // 新增：用于回溯查找的历史匹配记录
                let previousMatches = []; // 存储之前成功匹配的书卷和章节信息

                for (const line of lines) {
                    // 如果是空行，添加空行标记并继续
                    if (line.trim() === '') {
                        processedText += '<div>&nbsp;</div>';
                        continue;
                    }

                    let currentLine = '';
                    let lastIndex = 0;

                    let localBook = null; // 当前行内已解析的书卷上下文
                    let localChapter = null; // 当前行内已解析的章号上下文
                    inlineContextRegex.lastIndex = 0; // 重置行内上下文正则的匹配位置
                    let contextMatch;

                    // 尝试从当前行提取书卷和章的上下文 (例如："约翰福音三章...")
                    console.log(`🔍 悬停阅读-开始检查行内上下文: "${line}"`);
                    console.log(`🔍 悬停阅读-上下文正则表达式: ${inlineContextRegex.source}`);

                    // 🔧 调试：测试特定字符串的匹配
                    const testRegex = new RegExp(inlineContextRegex.source, 'g');
                    const testResult = testRegex.test("行传十三章");
                    console.log(`🔍 悬停阅读-测试"行传十三章"匹配:`, testResult);

                    while ((contextMatch = inlineContextRegex.exec(line)) !== null) {
                        console.log(`🔍 悬停阅读-上下文匹配: "${contextMatch[0]}", 书名组: "${contextMatch[1]}", 章号组: "${contextMatch[2]}"`);
                        const bookNameOrAbbrCtx = contextMatch[1]; // 书名或简称
                        const chapterStrCtx = contextMatch[2]; // 中文章号字符串
                        // 将书名转换为标准简称
                        const bookCandidateCtx = fullBookNameToAbbrMap[bookNameOrAbbrCtx] || bookMap[bookNameOrAbbrCtx] || bookNameOrAbbrCtx;

                        if (bookCandidateCtx) { // 如果是有效的书名
                            const chapterNumCtx = chineseToArabic(chapterStrCtx); // 中文章号转为阿拉伯数字
                            if (chapterNumCtx > 0) { // 如果是有效的章号
                                localBook = bookCandidateCtx;    // 设置行内书卷上下文
                                localChapter = chapterNumCtx;  // 设置行内章号上下文
                                // 更新全局（函数级）上下文，供后续无明确书卷/章号的引用使用
                                lastBook = localBook;
                                lastChapter = localChapter;
                                console.log(`📖 悬停阅读-上下文设置: localBook="${localBook}", localChapter=${localChapter}, lastBook="${lastBook}", lastChapter=${lastChapter}`);

                                // 🔧 修复：将上下文设置也添加到历史匹配记录中，确保回溯功能能使用最新的上下文
                                previousMatches.push({
                                    book: bookCandidateCtx,
                                    chapter: chapterNumCtx,
                                    verse: 1, // 上下文设置时使用默认节号1
                                    isContext: true // 标记这是上下文设置，不是具体的经文引用
                                });
                                console.log(`🔄 悬停阅读-上下文添加到历史匹配: ${bookCandidateCtx} ${chapterNumCtx}章`);

                                // 只保留最近的10个匹配记录，避免内存过度使用
                                if (previousMatches.length > 10) {
                                    previousMatches.splice(0, previousMatches.length - 10);
                                }
                            }
                        }
                    }

                    // 🔧 新增：检查单独的书名（没有章号）作为上下文 - 如"罗马书"
                    const bookOnlyRegex = RegexFactory.createBookOnlyContextRegex(bookAbbrRegexPart, fullBookNamesRegexPart);
                    console.log(`🔍 悬停阅读-检查单独书名上下文: ${bookOnlyRegex.source}`);
                    let bookOnlyMatch;
                    while ((bookOnlyMatch = bookOnlyRegex.exec(line)) !== null) {
                        console.log(`🔍 悬停阅读-单独书名匹配: "${bookOnlyMatch[0]}", 书名组: "${bookOnlyMatch[1]}"`);
                        const bookNameOrAbbrOnly = bookOnlyMatch[1];
                        const bookCandidateOnly = fullBookNameToAbbrMap[bookNameOrAbbrOnly] || bookMap[bookNameOrAbbrOnly] || bookNameOrAbbrOnly;

                        if (bookCandidateOnly) {
                            // 设置为该书的第1章上下文
                            localBook = bookCandidateOnly;
                            localChapter = 1;
                            lastBook = localBook;
                            lastChapter = localChapter;
                            console.log(`📖 悬停阅读-单独书名上下文设置: localBook="${localBook}", localChapter=${localChapter} (默认第1章)`);

                            // 添加到历史匹配记录
                            previousMatches.push({
                                book: bookCandidateOnly,
                                chapter: 1,
                                verse: 1,
                                isContext: true,
                                isBookOnly: true // 标记这是单独书名上下文
                            });
                            console.log(`🔄 悬停阅读-单独书名添加到历史匹配: ${bookCandidateOnly} 1章 (默认)`);

                            // 只保留最近的10个匹配记录
                            if (previousMatches.length > 10) {
                                previousMatches.splice(0, previousMatches.length - 10);
                            }
                        }
                    }

                    let match; // 用于存储 refRegex 的匹配结果
                    refRegex.lastIndex = 0; // 重置主要引用正则的匹配位置

                    // 在当前行循环查找所有经文引用
                    while ((match = refRegex.exec(line)) !== null) {
                        // 添加引用前的文本
                        currentLine += escapeHtml(line.substring(lastIndex, match.index));

                        let currentBook = null;     // 当前匹配到的书卷
                        let chapterNum = 0;       // 当前匹配到的章号
                        let currentVerseStr = null; // 当前匹配到的节号字符串 (可能是单个数字或范围)
                        let currentSuffix = null;   // 当前匹配到的后缀 (例如 "下")

                        // 根据匹配到的不同正则组解析引用
                        if (match[1] && match[2] && match[3]) { // 格式1 匹配成功: 书名(全/简)+中文章+中文节
                            const bookNameOrAbbr = match[1].replace(/^参/, ''); // 移除可选的 "参" 前缀
                            currentBook = fullBookNameToAbbrMap[bookNameOrAbbr] || bookMap[bookNameOrAbbr] || bookNameOrAbbr;
                            if (!currentBook) {
                                currentLine += escapeHtml(match[0]); // 无效书名，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }
                            chapterNum = chineseToArabic(match[2]); // 中文章号转数字
                            currentVerseStr = parseChineseVerseRange(match[3]); // 解析中文节号或范围
                            currentSuffix = match[4]; // 获取后缀 "下"
                            if (!currentVerseStr || chapterNum === 0) {
                                currentLine += escapeHtml(match[0]); // 无效章或节，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            // 更新上下文
                            lastBook = currentBook; lastChapter = chapterNum;
                            localBook = currentBook; localChapter = chapterNum;
                        } else if (match[5] && match[6] && match[7]) { // 格式2 匹配成功: 书名(简)+中文章+阿拉伯节
                            let rawBook = match[5].replace(/^参/, '');
                            currentBook = bookMap[rawBook] || rawBook;
                            chapterNum = chineseToArabic(match[6]);
                            currentVerseStr = match[7]; // 阿拉伯数字节号，无需 parseChineseVerseRange
                            currentSuffix = match[8];
                            if (!currentBook || chapterNum === 0) {
                                currentLine += escapeHtml(match[0]); // 无效书名或章号，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            lastBook = currentBook; lastChapter = chapterNum;
                            localBook = currentBook; localChapter = chapterNum;
                        } else if (match[9] && match[10]) { // 格式3 匹配成功: 中文章+中文节 (依赖上下文)
                            const contextBook = localBook || lastBook; // 优先使用行内上下文，其次使用全局（函数级）上下文
                            if (!contextBook) {
                                currentLine += escapeHtml(match[0]); // 没有上下文无法确定书卷，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }
                            currentBook = contextBook;
                            chapterNum = chineseToArabic(match[9]);
                            currentVerseStr = parseChineseVerseRange(match[10]);
                            currentSuffix = match[11];
                            if (!currentVerseStr || chapterNum === 0) {
                                currentLine += escapeHtml(match[0]); // 无效章或节，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            lastBook = currentBook; lastChapter = chapterNum;
                            localBook = currentBook; localChapter = chapterNum; // 如果是行内匹配，也更新行内上下文
                        } else if (match[12] && match[13]) { // 格式4 匹配成功: 中文章+阿拉伯节 (依赖上下文)
                            const contextBook = localBook || lastBook;
                            if (!contextBook) {
                                currentLine += escapeHtml(match[0]); // 没有上下文无法确定书卷，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }
                            currentBook = contextBook;
                            chapterNum = chineseToArabic(match[12]);
                            currentVerseStr = match[13]; // 阿拉伯数字节号
                            currentSuffix = match[14];
                            if (chapterNum === 0) {
                                currentLine += escapeHtml(match[0]); // 无效章号，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            lastBook = currentBook; lastChapter = chapterNum;
                            localBook = currentBook; localChapter = chapterNum;
                        } else if (match[15] && match[16]) { // 格式5 匹配成功: 书名(简)+阿拉伯章[:：]?阿拉伯节
                            let rawBook = match[15].replace(/^参/, '');
                            currentBook = bookMap[rawBook] || rawBook;
                            let chapVersePart = match[16]; // 形如 "3:16" 或 "3" (单章书卷的节)
                            currentSuffix = match[17];

                            const chapVerseMatch = chapVersePart.match(REGEX.CHAPTER_VERSE_SEPARATOR); // 使用预定义正则
                            if (chapVerseMatch) {
                                chapterNum = parseInt(chapVerseMatch[1], 10);
                                currentVerseStr = chapVerseMatch[2].replace(/^[:：]/, ''); // 移除可能存在的分隔符
                            } else if (singleChapterBooks.has(currentBook)) { // 如果是单章书卷
                                chapterNum = 1; // 章号固定为1
                                currentVerseStr = chapVersePart; // 整段作为节号
                            } else { // 多章书卷，但没有明确章号分隔符 (如 "约 16")，尝试使用上下文中的章
                                const contextChapterP5 = localChapter || lastChapter;
                                if (contextChapterP5) {
                                    chapterNum = contextChapterP5;
                                    currentVerseStr = chapVersePart; // 整段作为节号
                                } else {
                                    // 如果是纯数字且不是范围，可能是章号，但模式要求节号，不明确则跳过
                                    if (REGEX.PURE_DIGIT.test(chapVersePart) && !REGEX.DIGIT_RANGE.test(chapVersePart)) {
                                        currentLine += escapeHtml(match[0]); // 无法确定是章号还是节号，保留原文
                                        lastIndex = match.index + match[0].length;
                                        continue;
                                    } else {
                                        currentLine += escapeHtml(match[0]); // 无法确定是章号还是节号，保留原文
                                        lastIndex = match.index + match[0].length;
                                        continue;
                                    }
                                }
                            }
                            if (!currentBook || chapterNum === 0) {
                                currentLine += escapeHtml(match[0]); // 无效书名或章号，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            // 更新上下文
                            lastBook = currentBook;
                            // 如果解析出的章号与当前行内或全局上下文不同，或者行内上下文不存在，则更新它们
                            if (chapterNum !== (localChapter || lastChapter) || !localChapter) {
                                lastChapter = chapterNum;
                                localBook = currentBook; // 同时更新行内书卷上下文
                                localChapter = chapterNum; // 更新行内章上下文
                            }

                        } else if (match[18] && match[19]) { // 新增匹配：单章书卷+节号(无章号)
                            let rawBook = match[18].replace(/^参/, '');
                            currentBook = bookMap[rawBook] || rawBook;
                            let verseStr = match[19];
                            currentSuffix = match[20]; // 后缀

                            // 明确检查是否为单章书卷的有效引用格式
                            if (isSingleChapterBookReference(currentBook, verseStr)) {
                                chapterNum = 1; // 章号固定为1
                                currentVerseStr = verseStr; // 节号

                                // 更新上下文
                                lastBook = currentBook;
                                lastChapter = chapterNum;
                                localBook = currentBook;
                                localChapter = chapterNum;
                            } else {
                                // 如果不是单章书卷，可能是格式5的变体，尝试使用上下文章号
                                const contextChapter = localChapter || lastChapter;
                                if (contextChapter) {
                                    chapterNum = contextChapter;
                                    currentVerseStr = verseStr;

                                    // 更新上下文
                                    lastBook = currentBook;
                                    localBook = currentBook;
                                    localChapter = contextChapter;
                                } else {
                                    currentLine += escapeHtml(match[0]); // 无法确定章号，保留原文
                                    lastIndex = match.index + match[0].length;
                                    continue; // 无法确定章号，跳过
                                }
                            }

                        } else if (match[21] && match[22]) { // 新增匹配：单章书卷+节号(无空格)
                            let rawBook = match[21].replace(/^参/, '');
                            currentBook = bookMap[rawBook] || rawBook;
                            let verseStr = match[22];
                            currentSuffix = match[23]; // 后缀

                            // 明确检查是否为单章书卷的有效引用格式
                            if (isSingleChapterBookReference(currentBook, verseStr)) {
                                chapterNum = 1; // 章号固定为1
                                currentVerseStr = verseStr; // 节号

                                // 更新上下文
                                lastBook = currentBook;
                                lastChapter = chapterNum;
                                localBook = currentBook;
                                localChapter = chapterNum;
                            } else {
                                // 如果不是单章书卷，可能是格式5的变体，尝试使用上下文章号
                                const contextChapter = localChapter || lastChapter;
                                if (contextChapter) {
                                    chapterNum = contextChapter;
                                    currentVerseStr = verseStr;

                                    // 更新上下文
                                    lastBook = currentBook;
                                    localBook = currentBook;
                                    localChapter = contextChapter;
                                } else {
                                    currentLine += escapeHtml(match[0]); // 无法确定章号，保留原文
                                    lastIndex = match.index + match[0].length;
                                    continue; // 无法确定章号，跳过
                                }
                            }
                        } else if (match[26] && match[27] && match[28] && match[29]) { // 🔧 新增格式：跨章引用 - 如"三章一节至四章三十一节"
                            console.log(`🔍 悬停阅读-跨章格式匹配: "${match[0]}", 起始章="${match[26]}", 起始节="${match[27]}", 结束章="${match[28]}", 结束节="${match[29]}"`);

                            // 依赖上下文确定书卷
                            const contextBook = localBook || lastBook;
                            if (!contextBook) {
                                console.log(`❌ 悬停阅读-跨章格式缺少书卷上下文: contextBook="${contextBook}"`);
                                currentLine += escapeHtml(match[0]); // 保留原文
                                lastIndex = match.index + match[0].length;
                                continue; // 没有书卷上下文无法处理跨章引用
                            }

                            currentBook = contextBook;
                            const startChapter = chineseToArabic(match[26]);
                            const startVerseStr = parseChineseVerseRange(match[27]);
                            const endChapter = chineseToArabic(match[28]);
                            const endVerseStr = parseChineseVerseRange(match[29]);
                            currentSuffix = match[30];

                            if (!startVerseStr || !endVerseStr || startChapter === 0 || endChapter === 0) {
                                console.log(`❌ 悬停阅读-跨章格式解析失败: startChapter=${startChapter}, startVerseStr="${startVerseStr}", endChapter=${endChapter}, endVerseStr="${endVerseStr}"`);
                                currentLine += escapeHtml(match[0]); // 保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            console.log(`📖 悬停阅读-跨章格式解析结果: 书名="${currentBook}", 起始=${startChapter}:${startVerseStr}, 结束=${endChapter}:${endVerseStr}`);

                            // 构建跨章引用格式
                            const startVerse = parseInt(startVerseStr.split('-')[0], 10);
                            const endVerse = parseInt(endVerseStr.includes('-') ? endVerseStr.split('-')[1] : endVerseStr, 10);
                            const startRef = ReferenceFormatManager.formatReference(currentBook, startChapter, startVerse);
                            const endRef = ReferenceFormatManager.formatReference(currentBook, endChapter, endVerse);
                            const crossChapterRef = `${startRef}~${endRef}`;

                            // 获取起始和结束经文内容
                            let combinedVerseText = null;
                            const startVerseKey = `${currentBook} ${startChapter}:${startVerse}`;
                            const endVerseKey = `${currentBook} ${endChapter}:${endVerse}`;
                            const startVerseText = hoverReadBibleData[startVerseKey];
                            const endVerseText = hoverReadBibleData[endVerseKey];

                            if (startVerseText && endVerseText) {
                                combinedVerseText = `起始：${startVerseText} ... 结束：${endVerseText}`;
                            } else if (startVerseText) {
                                combinedVerseText = `起始：${startVerseText}`;
                            } else if (endVerseText) {
                                combinedVerseText = `结束：${endVerseText}`;
                            }

                            // 设置当前引用信息（用于统一的悬停预览生成）
                            currentVerseStr = `${startVerse}~${endVerse}`;
                            chapterNum = startChapter; // 使用起始章作为主要章节

                            // 更新上下文为结束章节
                            lastBook = currentBook;
                            lastChapter = endChapter;
                            localBook = currentBook;
                            localChapter = endChapter;

                            console.log(`📝 悬停阅读-跨章格式处理完成: ${crossChapterRef}`);

                        } else if (match[32]) { // 🔧 格式9：仅中文节号范围（依赖上下文）- 索引更新
                            const chineseVerseRange = match[32];
                            console.log(`🔍 悬停阅读-格式9匹配: "${match[0]}", 中文节号范围: "${chineseVerseRange}"`);

                            // 🔧 添加上下文过滤：避免匹配普通文本中的中文数字
                            const startIndex = match.index;
                            const endIndex = match.index + match[0].length;
                            const prevChar = startIndex > 0 ? line[startIndex - 1] : '^';
                            const nextChar = endIndex < line.length ? line[endIndex] : '$';

                            // 检查是否像列表项：中文数字，前面是行首，后面是全角空格
                            const looksLikeChineseList =
                                (prevChar === '^' || /^\s*$/.test(prevChar)) &&
                                /^[　\s\t]/.test(nextChar);

                            // 检查是否在不合适的上下文中
                            const isInappropriateContext =
                                // 列表项检测：如"一　"、"二　"、"三　"等
                                looksLikeChineseList ||
                                // 前面是"第"、"这"、"那"等
                                /[第这那其]$/.test(prevChar) ||
                                // 前面是"用"、"有"、"是"等
                                /[用有是为在于]$/.test(prevChar) ||
                                // 前面是"之"、"的"、"了"等助词
                                /[之的了得过来去]$/.test(prevChar) ||
                                // 后面是"章"、"个"、"种"、"样"、"言"、"语"、"组"、"类"、"部"、"方"等
                                /^[章个种样言语次遍回趟组类部方面点条项]/.test(nextChar) ||
                                // 后面是"天"、"年"、"月"、"日"、"时"等时间单位
                                /^[天年月日时分秒]/.test(nextChar) ||
                                // 后面是"位"、"本"、"段"、"对"、"篇"等量词
                                /^[位本段对篇首]/.test(nextChar) ||
                                // 后面是"神"、"全"、"我"等常见段落开头词汇（序号后的内容）
                                /^[神全我人主耶基督圣灵父子爱信望盼救恩生命真理道路]/.test(nextChar) ||
                                // 前后都是中文字符（可能是词语的一部分）
                                (/[\u4e00-\u9fff]$/.test(prevChar) && /^[\u4e00-\u9fff]/.test(nextChar)) ||
                                // 前面是中文字符且不是明确的经文引用上下文（排除连接词和标点）
                                (/[\u4e00-\u9fff]$/.test(prevChar) && !/[章节和与及，、；]$/.test(prevChar));

                            if (isInappropriateContext) {
                                console.log(`❌ 悬停阅读-格式9过滤: "${match[0]}"在不合适的上下文中，前字符="${prevChar}", 后字符="${nextChar}"`);
                                currentLine += escapeHtml(match[0]); // 保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            // 依赖上下文确定书卷和章节
                            const contextBook = localBook || lastBook;
                            const contextChapter = localChapter || lastChapter;

                            if (!contextBook || !contextChapter) {
                                console.log(`❌ 悬停阅读-格式9缺少上下文: contextBook="${contextBook}", contextChapter=${contextChapter}`);
                                currentLine += escapeHtml(match[0]); // 保留原文
                                lastIndex = match.index + match[0].length;
                                continue; // 没有上下文无法确定书卷和章节
                            }

                            currentBook = contextBook;
                            chapterNum = contextChapter;
                            currentSuffix = match[33];

                            // 解析中文节号范围
                            currentVerseStr = parseChineseVerseRange(chineseVerseRange);
                            if (currentVerseStr) {
                                console.log(`📖 悬停阅读-格式9解析结果: 书名="${currentBook}", 章号=${chapterNum}, 节号范围="${currentVerseStr}"`);
                                console.log(`📝 悬停阅读-格式9处理完成: ${currentBook} ${chapterNum}:${currentVerseStr}`);

                                // 更新上下文
                                lastBook = currentBook;
                                lastChapter = chapterNum;
                                localBook = currentBook;
                                localChapter = chapterNum;
                            } else {
                                console.log(`❌ 悬停阅读-格式9无法解析中文节号范围: "${chineseVerseRange}"`);
                                currentLine += escapeHtml(match[0]); // 保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }
                        } else if (match[34]) { // 格式10 匹配成功: 仅阿拉伯节号 (支持回溯查找)
                            const verseStrOnly = match[34];
                            console.log(`🔍 悬停阅读-格式6匹配: "${match[0]}", 节号: "${verseStrOnly}"`);
                            console.log(`📊 悬停阅读-当前上下文状态: localBook="${localBook}", localChapter=${localChapter}, lastBook="${lastBook}", lastChapter=${lastChapter}`);
                            console.log(`📚 悬停阅读-历史匹配记录:`, previousMatches.map(m => `${m.book} ${m.chapter}:${m.verse}${m.isContext ? '(上下文)' : ''}`));

                            // 🔧 修复：智能选择最合适的上下文 - 基于上下文新鲜度
                            let contextBook = null;
                            let contextChapter = null;
                            let useHistoryMatch = false;

                            // 获取当前上下文
                            const currentContext = {
                                book: localBook || lastBook,
                                chapter: localChapter || lastChapter
                            };

                            // 🔧 修复：获取历史匹配中的最新上下文（优先考虑最新记录）
                            let latestHistoryContext = null;
                            if (previousMatches.length > 0) {
                                // 首先尝试使用最后一个记录（最新的引用）
                                const lastMatch = previousMatches[previousMatches.length - 1];
                                latestHistoryContext = {
                                    book: lastMatch.book,
                                    chapter: lastMatch.chapter,
                                    isBookOnly: lastMatch.isBookOnly || false,
                                    index: previousMatches.length - 1,
                                    isLatestMatch: true
                                };

                                // 如果最后一个记录不是上下文记录，也查找最后一个上下文记录作为备选
                                if (!lastMatch.isContext) {
                                    for (let i = previousMatches.length - 1; i >= 0; i--) {
                                        if (previousMatches[i].isContext) {
                                            // 如果找到的上下文记录比最后一个记录更新，使用上下文记录
                                            if (i > previousMatches.length - 3) { // 允许一定的容差
                                                latestHistoryContext = {
                                                    book: previousMatches[i].book,
                                                    chapter: previousMatches[i].chapter,
                                                    isBookOnly: previousMatches[i].isBookOnly || false,
                                                    index: i,
                                                    isLatestMatch: false
                                                };
                                            }
                                            break;
                                        }
                                    }
                                }
                            }

                            // 🔧 修复：智能上下文选择 - 平衡当前上下文和历史上下文
                            // 1. 如果历史上下文比当前上下文更新，使用历史上下文
                            // 2. 如果当前上下文有效且没有更新的历史上下文，使用当前上下文
                            // 3. 如果当前上下文无效，使用历史上下文

                            // 🔧 修复：优先使用当前上下文，只有当前上下文无效时才使用历史上下文
                            console.log(`🔍 悬停阅读-上下文比较: 当前(${currentContext.book} ${currentContext.chapter}) vs 历史(${latestHistoryContext?.book} ${latestHistoryContext?.chapter})`);

                            if (currentContext.book && currentContext.chapter) {
                                // 🔧 修复：优先使用当前上下文（行内或全局上下文）
                                contextBook = currentContext.book;
                                contextChapter = currentContext.chapter;
                                console.log(`📊 悬停阅读-使用当前上下文: contextBook="${contextBook}", contextChapter=${contextChapter} for 节号 ${verseStrOnly}`);
                                console.log(`📋 悬停阅读-选择原因: 当前上下文有效，优先使用`);
                            } else if (latestHistoryContext) {
                                // 当前上下文无效，使用历史上下文
                                contextBook = latestHistoryContext.book;
                                contextChapter = latestHistoryContext.chapter;
                                useHistoryMatch = true;
                                console.log(`🔄 悬停阅读-当前上下文无效，使用历史上下文: ${contextBook} ${contextChapter}章${latestHistoryContext.isBookOnly ? '(单独书名)' : ''} for 节号 ${verseStrOnly}`);
                                console.log(`📋 悬停阅读-选择原因: 当前上下文无效`);
                            } else {
                                console.log(`❌ 悬停阅读-无可用上下文 for 节号 ${verseStrOnly}`);
                            }

                            if (!contextBook || !contextChapter) {
                                // 如果仍然没有上下文，则不处理纯数字节号，避免误匹配
                                currentLine += escapeHtml(match[0]);
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            // 防止将列表编号 (如 "1." "2、") 误认为节号
                            const startIndex = match.index; // 匹配项在行中的起始位置
                            const prevChar = startIndex > 0 ? line[startIndex - 1] : '^'; // 匹配项前的字符 (或行首)
                            let nextCharIndex = match.index + match[0].length; // 匹配项后的字符索引
                            const nextChar = line[nextCharIndex] || '$'; // 匹配项后的字符 (或行尾)

                            // 判断是否像列表项：纯数字，前后是空格/标点/行首尾
                            const looksLikeList = REGEX.PURE_DIGIT.test(verseStrOnly) &&
                                (prevChar === '^' || REGEX.LOOKS_LIKE_LIST_PREV.test(prevChar)) &&
                                (nextChar === '.' || nextChar === '、' || REGEX.LOOKS_LIKE_LIST_NEXT.test(nextChar) || nextChar === '$');

                            // 判断前面是否有明确的书卷章节上下文 (例如 "约翰福音三章1" 或 "约3:1")
                            const hasBookChapterContextImmediatelyBefore =
                                line.substring(0, startIndex).match(REGEX.CHAPTER_CONTEXT) ||
                                line.substring(0, startIndex).match(REGEX.BOOK_CHAPTER_CONTEXT);

                            if (looksLikeList && !hasBookChapterContextImmediatelyBefore) {
                                // 如果看起来像列表项，并且前面没有紧邻的书卷章节上下文，则保留原文
                                currentLine += escapeHtml(match[0]);
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            currentBook = contextBook;
                            chapterNum = contextChapter;
                            currentVerseStr = verseStrOnly;
                            currentSuffix = match[35];
                            console.log(`✅ 悬停阅读-格式6设置最终值: currentBook="${currentBook}", chapterNum=${chapterNum}, currentVerseStr="${currentVerseStr}"`);
                        } else if (match[29] && match[30]) { // 新增格式：书名(全名)+中文节号（无章号，适用于单章书卷）- 如"犹大书一节"
                            console.log(`🔍 悬停阅读-新格式匹配: "${match[0]}", 书名组: "${match[29]}", 节号组: "${match[30]}"`);
                            const bookNameOrAbbr = match[29].replace(/^参/, ''); // 移除可选的 "参" 前缀
                            currentBook = fullBookNameToAbbrMap[bookNameOrAbbr] || bookMap[bookNameOrAbbr] || bookNameOrAbbr;
                            console.log(`📖 悬停阅读-书名映射: "${bookNameOrAbbr}" → "${currentBook}"`);

                            if (!currentBook) {
                                console.log(`❌ 悬停阅读-新格式无效书名: "${bookNameOrAbbr}"`);
                                currentLine += escapeHtml(match[0]); // 无效书名，保留原文
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            // 检查是否可能是章号引用的误匹配
                            const fullMatch = match[0];
                            const matchStartIndex = match.index;
                            const matchEndIndex = matchStartIndex + fullMatch.length;
                            const nextChar = line[matchEndIndex] || '';

                            // 更严格的章号引用检测
                            const isLikelyChapterReference =
                                fullMatch.includes('章') || // 包含"章"字
                                nextChar === '章' || // 后面紧跟"章"字
                                (match[30] && /^[一二三四五六七八九十百千〇零]+$/.test(match[30]) &&
                                    !fullMatch.includes('节') && !singleChapterBooks.has(currentBook) &&
                                    (nextChar === '章' || nextChar === '来' || nextChar === '看' || nextChar === '说')); // 常见的章号引用后续词

                            if (isLikelyChapterReference) {
                                console.log(`⚠️ 悬停阅读-疑似章号引用误匹配，跳过新格式处理: "${fullMatch}", 后续字符: "${nextChar}"`);
                                currentLine += escapeHtml(match[0]); // 保留原文，让其他格式处理
                                lastIndex = match.index + match[0].length;
                                continue;
                            }

                            // 检查是否为单章书卷
                            if (singleChapterBooks.has(currentBook)) {
                                console.log(`✅ 悬停阅读-识别为单章书卷: "${currentBook}"`);
                                chapterNum = 1; // 单章书卷固定为第1章
                                currentVerseStr = parseChineseVerseRange(match[30]); // 解析中文节号或范围
                                currentSuffix = match[31]; // 获取后缀 "下"
                                console.log(`📝 悬停阅读-解析结果: 章号=${chapterNum}, 节号="${currentVerseStr}", 后缀="${currentSuffix}"`);

                                if (!currentVerseStr) {
                                    console.log(`❌ 悬停阅读-无效节号: "${match[30]}"`);
                                    currentLine += escapeHtml(match[0]); // 无效节号，保留原文
                                    lastIndex = match.index + match[0].length;
                                    continue;
                                }

                                // 更新上下文
                                lastBook = currentBook;
                                lastChapter = chapterNum;
                                localBook = currentBook;
                                localChapter = chapterNum;
                            } else {
                                console.log(`⚠️ 悬停阅读-非单章书卷且非明确节号引用，跳过新格式处理: "${currentBook}"`);
                                currentLine += escapeHtml(match[0]); // 保留原文，让其他格式处理
                                lastIndex = match.index + match[0].length;
                                continue;
                            }
                        } else {
                            // 未匹配到任何已知格式，保留原文
                            currentLine += escapeHtml(match[0]);
                            lastIndex = match.index + match[0].length;
                            continue;
                        }

                        // 如果成功解析出书、章、节
                        if (currentBook && chapterNum > 0 && currentVerseStr) {
                            const originalRef = match[0]; // 原始引用文本
                            let formattedRef = ReferenceFormatManager.formatReference(currentBook, chapterNum, currentVerseStr) + (currentSuffix ? currentSuffix : ''); // 格式化后的引用

                            // 查找经文文本 - 使用回溯功能
                            let verseText = '';
                            let backtrackInfo = null;
                            const rangeMatch = currentVerseStr.match(REGEX.AR_NUM_RANGE_MATCH);

                            if (rangeMatch) {
                                // 如果是范围，获取范围内所有节的经文，每节一行
                                const startVerse = parseInt(rangeMatch[1], 10);
                                const endVerse = parseInt(rangeMatch[2], 10);

                                if (!isNaN(startVerse) && !isNaN(endVerse) && startVerse <= endVerse) {
                                    // 收集所有节经文
                                    let verseLines = [];
                                    let hasBacktrack = false;
                                    let backtrackInfos = [];

                                    for (let v = startVerse; v <= endVerse; v++) {
                                        // 使用回溯查找功能
                                        const searchResult = VerseSearcher.findVerseWithBacktrack(
                                            currentBook, chapterNum, v, hoverReadBibleData, previousMatches
                                        );

                                        if (searchResult.text) {
                                            // 每节经文作为一个独立元素添加到数组，增加明显的边距和样式
                                            verseLines.push(`<div style="margin-bottom: 8px; padding-bottom: 4px; display: block;"><span class="verse-num" style="font-weight: bold; color: #4CAF50; margin-right: 4px;">${v}</span> ${escapeHtml(searchResult.text)}</div>`);

                                            // 记录回溯信息
                                            if (searchResult.backtracked) {
                                                hasBacktrack = true;
                                                backtrackInfos.push(`第${v}节: ${searchResult.backtrackInfo}`);
                                            }
                                        }
                                    }

                                    // 将所有节经文连接成HTML字符串
                                    verseText = verseLines.join('');

                                    // 如果有回溯信息，合并显示
                                    if (hasBacktrack && backtrackInfos.length > 0) {
                                        backtrackInfo = backtrackInfos.join('; ');
                                    }
                                }
                            } else {
                                // 如果是单节，使用回溯查找
                                const singleVerseMatch = currentVerseStr.match(REGEX.SINGLE_AR_NUM);
                                if (singleVerseMatch) {
                                    const verseNum = parseInt(singleVerseMatch[1], 10);

                                    // 使用回溯查找功能
                                    const searchResult = VerseSearcher.findVerseWithBacktrack(
                                        currentBook, chapterNum, verseNum, hoverReadBibleData, previousMatches
                                    );

                                    if (searchResult.text) {
                                        verseText = `<div style="margin-bottom: 8px; padding-bottom: 4px; display: block;"><span class="verse-num" style="font-weight: bold; color: #4CAF50; margin-right: 4px;">${verseNum}</span> ${escapeHtml(searchResult.text)}</div>`;

                                        // 记录回溯信息
                                        if (searchResult.backtracked) {
                                            backtrackInfo = searchResult.backtrackInfo;
                                        }
                                    }
                                }
                            }

                            // 创建带有悬停预览的引用元素
                            currentLine += `<span class="reference-hover" style="border-bottom: 1px dashed #4CAF50; cursor: pointer;">${escapeHtml(originalRef)}`;
                            currentLine += `<span class="verse-preview" style="display: none; position: absolute; bottom: 100%; left: 0; background-color: #fff; border: 1px solid #ccc; box-shadow: 0 2px 8px rgba(0,0,0,0.2); padding: 10px; border-radius: 4px; width: 350px; z-index: 100; font-style: italic; color: #155724; max-height: 300px; overflow-y: auto;">`;
                            currentLine += `<div class="verse-preview-header" style="font-weight: bold; margin-bottom: 10px; color: #333; border-bottom: 1px solid #eee; padding-bottom: 5px;">${escapeHtml(formattedRef)}</div>`;

                            if (verseText) {
                                currentLine += `<div class="verse-preview-text" style="line-height: 1.5;">${verseText}</div>`;
                                // 如果有回溯信息，显示回溯提示
                                if (backtrackInfo) {
                                    currentLine += `<div style="color: #856404; font-size: 0.85em; font-style: italic; margin-top: 8px; padding-top: 5px; border-top: 1px dotted #ddd;">[回溯查找] ${escapeHtml(backtrackInfo)}</div>`;
                                }
                            } else {
                                currentLine += `<div class="verse-preview-not-found" style="color: #721c24; font-style: italic;">经文未在JSON中找到</div>`;
                            }

                            currentLine += `</span></span>`;
                        } else {
                            // 如果解析失败，保留原文
                            currentLine += escapeHtml(match[0]);
                        }

                        // 更新历史匹配记录（用于回溯查找）
                        if (currentBook && chapterNum > 0) {
                            previousMatches.push({
                                book: currentBook,
                                chapter: chapterNum,
                                verse: 1 // 对于悬停阅读，我们主要关心书卷和章节上下文
                            });

                            // 只保留最近的10个匹配记录，避免内存过度使用
                            if (previousMatches.length > 10) {
                                previousMatches.splice(0, previousMatches.length - 10);
                            }
                        }

                        lastIndex = match.index + match[0].length;
                    }

                    // 添加行尾剩余文本
                    currentLine += escapeHtml(line.substring(lastIndex));
                    processedText += `<div>${currentLine}</div>`;
                }

                // 将处理后的文本设置到内容区域
                hoverContentElement.innerHTML = processedText;

            } catch (error) {
                hoverContentElement.innerHTML = `<div style="color: #721c24; background-color: #f8d7da; border-left: 3px solid #dc3545; padding: 10px; margin: 10px 0;">处理文本时出错: ${escapeHtml(error.message)}<br><pre>${escapeHtml(error.stack)}</pre></div>`;
            }
        }

        // 新增一个专门用于检测单章书卷的节号引用的函数
        function isSingleChapterBookReference(book, verseStr) {
            // 检查书卷是否为单章书卷
            if (singleChapterBooks.has(book)) {
                // 验证节号是否为有效的数字或范围
                const singleVerseMatch = verseStr.match(REGEX.SINGLE_AR_NUM);
                const rangeMatch = verseStr.match(REGEX.AR_NUM_RANGE_MATCH);

                return singleVerseMatch || rangeMatch;
            }
            return false;
        }

        // 添加全屏功能
        document.addEventListener('DOMContentLoaded', () => {
            const hoverContent = document.getElementById('hoverContent');
            const fullscreenBtn = document.getElementById('hoverFullscreenBtn');
            const contentPanel = fullscreenBtn.closest('.content-panel');

            // 移除contenteditable属性，确保结果区域不可编辑
            hoverContent.removeAttribute('contenteditable');

            let isFullscreen = false;

            fullscreenBtn.addEventListener('click', () => {
                isFullscreen = !isFullscreen;

                if (isFullscreen) {
                    // 进入全屏模式
                    contentPanel.classList.add('fullscreen-mode');
                    fullscreenBtn.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
                        </svg>
                        退出全屏
                    `;
                    // 调整内容区域高度以适应全屏
                    hoverContent.style.height = 'calc(100vh - 100px)';
                } else {
                    // 退出全屏模式
                    contentPanel.classList.remove('fullscreen-mode');
                    fullscreenBtn.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                        </svg>
                        全屏
                    `;
                    // 恢复原来的高度
                    hoverContent.style.height = 'calc(100vh - 350px)';
                }
            });

            // 添加ESC键退出全屏功能
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && isFullscreen) {
                    isFullscreen = false;
                    contentPanel.classList.remove('fullscreen-mode');
                    fullscreenBtn.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                        </svg>
                        全屏
                    `;
                    hoverContent.style.height = 'calc(100vh - 350px)';
                }
            });
        });

        // "悬停阅读"标签页的输入框
        document.getElementById('hoverOriginal').addEventListener('input', (event) => {
            const text = event.target.value;
            if (DataManager.isDataLoaded()) { // 如果JSON已加载
                try {
                    processHoverReadContent(text);
                } catch (error) {
                    document.getElementById('hoverContent').innerHTML = `<div class="line-group"><div class="scripture-not-found">处理文本时出错: ${escapeHtml(error.message)}<br><pre>${escapeHtml(error.stack)}</pre></div></div>`;
                }
            } else {
                document.getElementById('hoverContent').innerHTML = '<div class="line-group"><div class="scripture-not-found">请先加载JSON文件才能进行处理。</div></div>';
            }
        });

        // 为悬停阅读标签页添加鼠标事件处理
        document.addEventListener('DOMContentLoaded', () => {
            // 为悬停阅读区域添加鼠标事件委托
            document.getElementById('hoverContent').addEventListener('mouseover', (event) => {
                // 查找事件目标或其祖先元素中的reference-hover元素
                let target = event.target;
                while (target && target !== document.getElementById('hoverContent')) {
                    if (target.classList && target.classList.contains('reference-hover')) {
                        // 找到预览元素
                        const preview = target.querySelector('.verse-preview');
                        if (preview) {
                            // 显示预览
                            preview.style.display = 'block';

                            // 确保预览框不会超出屏幕
                            const rect = preview.getBoundingClientRect();
                            const viewportWidth = window.innerWidth;
                            const viewportHeight = window.innerHeight;

                            // 处理水平位置
                            if (rect.right > viewportWidth) {
                                preview.style.left = 'auto';
                                preview.style.right = '0';
                            } else {
                                preview.style.left = '0';
                                preview.style.right = 'auto';
                            }

                            // 处理垂直位置
                            if (rect.top < 0 || rect.bottom > viewportHeight) {
                                preview.style.bottom = 'auto';
                                preview.style.top = '100%';
                            } else {
                                preview.style.bottom = '100%';
                                preview.style.top = 'auto';
                            }
                        }
                        break;
                    }
                    target = target.parentElement;
                }
            });

            document.getElementById('hoverContent').addEventListener('mouseout', (event) => {
                // 查找事件目标或其祖先元素中的reference-hover元素
                let target = event.target;
                let relatedTarget = event.relatedTarget;

                // 检查是否真的离开了引用元素或其预览
                while (target && target !== document.getElementById('hoverContent')) {
                    if (target.classList && target.classList.contains('reference-hover')) {
                        // 检查鼠标是否移动到预览元素内部
                        let isMovingToPreview = false;
                        let current = relatedTarget;

                        while (current && current !== document.getElementById('hoverContent')) {
                            if (current === target || current.parentElement === target) {
                                isMovingToPreview = true;
                                break;
                            }
                            current = current.parentElement;
                        }

                        if (!isMovingToPreview) {
                            // 如果不是移动到预览内部，则隐藏预览
                            const preview = target.querySelector('.verse-preview');
                            if (preview) {
                                preview.style.display = 'none';
                            }
                        }
                        break;
                    }
                    target = target.parentElement;
                }
            });
        });

        // --- 性能优化初始化 ---
        // 页面加载完成后自动加载圣经数据并初始化优化组件
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 开始初始化性能优化组件...');

            // 初始化性能监控
            PerformanceMonitor.setMemoryBaseline();
            console.log('✅ 性能监控初始化完成');

            // 初始化进度管理器
            ProgressManager.init();
            console.log('✅ 进度管理器初始化完成');

            // 初始化大文本检测器
            LargeTextDetector.init();
            console.log('✅ 大文本检测器初始化完成');

            // 自动加载圣经数据
            autoLoadBibJson();

            // 延迟初始化Worker（避免阻塞页面加载）
            setTimeout(async () => {
                try {
                    console.log('正在初始化Web Worker...');
                    const workerInitialized = await TextProcessingOptimizer.initWorkers();
                    if (workerInitialized) {
                        console.log('✅ Web Worker初始化成功，支持高性能文本处理');
                    } else {
                        console.log('⚠️ Web Worker不可用，将使用主线程处理');
                    }
                } catch (error) {
                    console.error('❌ Web Worker初始化失败:', error);
                }
            }, 1000);

            // 添加键盘快捷键支持
            document.addEventListener('keydown', (e) => {
                // Ctrl+Shift+P: 显示/隐藏性能面板
                if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                    e.preventDefault();
                    if (PerformancePanel.isVisible) {
                        PerformancePanel.hide();
                        console.log('性能面板已隐藏');
                    } else {
                        PerformancePanel.show();
                        PerformancePanel.update({
                            speed: 0,
                            memory: PerformanceMonitor.getCurrentMemoryUsage(),
                            workers: `0/${TextProcessingOptimizer.workers.length}`,
                            cache: RegexOptimizer ? RegexOptimizer.getCacheStats().hitRate : 0
                        });
                        console.log('性能面板已显示 (Ctrl+Shift+P 切换)');
                    }
                }

                // Esc: 取消当前处理
                if (e.key === 'Escape' && ProgressManager.isVisible) {
                    e.preventDefault();
                    ProgressManager.cancel();
                    console.log('用户取消了当前处理');
                }
            });

            // 添加内存监控
            setInterval(() => {
                if (PerformanceMonitor.checkMemoryPressure()) {
                    console.warn('⚠️ 检测到内存压力，建议清理缓存');
                    // 自动清理缓存
                    if (RegexOptimizer && RegexOptimizer.clearCache) {
                        RegexOptimizer.clearCache();
                        console.log('已自动清理正则表达式缓存');
                    }
                }
            }, 30000); // 每30秒检查一次

            console.log('🎉 性能优化组件初始化完成！');
            console.log('💡 提示：');
            console.log('  - Ctrl+Shift+P: 显示/隐藏性能监控面板');
            console.log('  - Esc: 取消当前文本处理');
            console.log('  - 大文本会自动使用优化处理模式');
        });
    </script>

    <!-- 引入导出HTML功能的JS文件 -->
    <script src="export-html.js"></script>
</body>

</html>