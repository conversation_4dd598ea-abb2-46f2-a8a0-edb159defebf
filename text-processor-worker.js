// Web Worker for text processing
// 处理大文本的Web Worker，避免主线程阻塞

// Worker内部的数据存储
let bibleData = null;
let bookMap = null;
let fullBookNameToAbbrMap = null;
let bookChapterLimits = null;
let singleChapterBooks = null;

// 正则表达式缓存
const regexCache = new Map();

// 预定义的正则表达式模式
const REGEX_PATTERNS = {
    CN_NUM: '[一二三四五六七八九十百千〇零]+',
    AR_NUM_RANGE: '\\d+(?:[~～˜∽-]\\d+)?',
    SINGLE_AR_NUM: /^(\d+)$/,
    AR_NUM_RANGE_MATCH: /^(\d+)[~～˜∽-](\d+)$/,
    PURE_DIGIT: /^\d+$/,
    VERSE_KEY: /^(.+) (\d+):(\d+)$/
};

// 初始化中文节号范围正则
REGEX_PATTERNS.CN_VERSE_RANGE = `${REGEX_PATTERNS.CN_NUM}(?:(?:至|到|-|~)?${REGEX_PATTERNS.CN_NUM})?`;
REGEX_PATTERNS.CHINESE_VERSE_RANGE = new RegExp(`^(${REGEX_PATTERNS.CN_NUM})(?:(?:至|到|-|~)?(${REGEX_PATTERNS.CN_NUM}))?$`);

// 优化的正则表达式创建函数
function createOptimizedRegex(pattern, flags = '', cacheKey = null) {
    const key = cacheKey || `${pattern}_${flags}`;
    
    if (regexCache.has(key)) {
        return regexCache.get(key);
    }
    
    const regex = new RegExp(pattern, flags);
    regexCache.set(key, regex);
    return regex;
}

// 转义正则表达式特殊字符
function escapeRegex(str) {
    return str.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
}

// 中文数字转换为阿拉伯数字
function chineseToArabic(numStr) {
    if (!numStr || typeof numStr !== 'string') return 0;
    
    const chineseNums = {
        '〇': 0, '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10, '百': 100, '千': 1000
    };
    
    let result = 0;
    let temp = 0;
    let hasUnit = false;
    
    for (let i = 0; i < numStr.length; i++) {
        const char = numStr[i];
        const num = chineseNums[char];
        
        if (num === undefined) continue;
        
        if (num >= 10) {
            if (num === 10 && temp === 0 && i === 0) {
                temp = 1;
            }
            if (temp === 0) temp = 1;
            
            if (num === 1000 || num === 100) {
                result += temp * num;
                temp = 0;
            } else if (num === 10) {
                temp *= num;
            }
            hasUnit = true;
        } else {
            temp = temp * 10 + num;
        }
    }
    
    return result + temp;
}

// HTML转义函数
function escapeHtml(unsafe) {
    if (!unsafe || typeof unsafe !== 'string') return '';
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// 分块处理文本的核心函数
function processTextChunk(chunk, chunkIndex, totalChunks, options = {}) {
    const {
        withVerses = true,
        enableSmartCorrection = true,
        strictVerseValidation = true
    } = options;
    
    if (!chunk || !chunk.trim()) {
        return {
            chunkIndex,
            html: '',
            processed: true,
            error: null
        };
    }
    
    try {
        const lines = chunk.split('\n');
        let htmlParts = [];
        
        // 创建书卷正则表达式部分
        const bookAbbrKeys = bookMap ? Object.keys(bookMap).sort((a, b) => b.length - a.length) : [];
        const fullBookKeys = fullBookNameToAbbrMap ? Object.keys(fullBookNameToAbbrMap).sort((a, b) => b.length - a.length) : [];
        
        const bookAbbrRegexPart = bookAbbrKeys.map(escapeRegex).join('|');
        const fullBookNamesRegexPart = fullBookKeys.map(escapeRegex).join('|');
        
        // 构建复合引用正则表达式
        const refPatterns = [
            `(?:参)?(${fullBookNamesRegexPart}|${bookAbbrRegexPart})(${REGEX_PATTERNS.CN_NUM})章(${REGEX_PATTERNS.CN_VERSE_RANGE})(?:节)?(下)?`,
            `(?:参)?(${bookAbbrRegexPart})(${REGEX_PATTERNS.CN_NUM})(${REGEX_PATTERNS.AR_NUM_RANGE})(?:节)?(下)?`,
            `(${REGEX_PATTERNS.CN_NUM})章(${REGEX_PATTERNS.CN_VERSE_RANGE})(?:节)?(下)?`,
            `(${REGEX_PATTERNS.CN_NUM})(${REGEX_PATTERNS.AR_NUM_RANGE})(?:节)?(下)?`,
            `(?:参)?(${bookAbbrRegexPart})(\\d+[:：]?${REGEX_PATTERNS.AR_NUM_RANGE})(?:节)?(下)?`,
            `(?:参)?(${bookAbbrRegexPart})\\s+(${REGEX_PATTERNS.AR_NUM_RANGE})(?:节)?(下)?`,
            `(?:参)?(${bookAbbrRegexPart})(${REGEX_PATTERNS.AR_NUM_RANGE})(?:节)?(下)?`,
            `(${REGEX_PATTERNS.AR_NUM_RANGE})(?:节)?(下)?`
        ];
        
        const refRegex = createOptimizedRegex(refPatterns.join('|'), 'g', 'compound_reference');
        const inlineContextRegex = createOptimizedRegex(
            `(${fullBookNamesRegexPart}|${bookAbbrRegexPart})([一二三四五六七八九十百千〇零]+)章`,
            'g',
            'inline_context'
        );
        
        let lastBook = null;
        let lastChapter = null;
        
        // 处理每一行
        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
            const line = lines[lineIndex];
            htmlParts.push(`<div class="line-group">`);
            
            if (line.trim() === '') {
                htmlParts.push(`<div class="original-line-wrapper">${escapeHtml(line)}</div>`);
                htmlParts.push(`</div>`);
                continue;
            }
            
            // 查找行内上下文
            let localBook = lastBook;
            let localChapter = lastChapter;
            
            let contextMatch;
            inlineContextRegex.lastIndex = 0;
            while ((contextMatch = inlineContextRegex.exec(line)) !== null) {
                const foundBook = contextMatch[1];
                const foundChapterChinese = contextMatch[2];
                const foundChapterNum = chineseToArabic(foundChapterChinese);
                
                if (bookMap && bookMap[foundBook] && foundChapterNum > 0) {
                    localBook = bookMap[foundBook];
                    localChapter = foundChapterNum;
                }
            }
            
            // 查找引用
            const foundItems = [];
            let match;
            refRegex.lastIndex = 0;
            
            while ((match = refRegex.exec(line)) !== null) {
                // 解析匹配结果（简化版）
                let currentBook = null;
                let chapterNum = 0;
                let verseStr = null;
                let suffix = '';
                
                // 根据匹配的组来确定书卷、章节、节号
                for (let i = 1; i < match.length; i++) {
                    if (match[i] && bookMap && bookMap[match[i]]) {
                        currentBook = bookMap[match[i]];
                        break;
                    }
                }
                
                if (!currentBook) {
                    currentBook = localBook;
                }
                
                if (currentBook && withVerses && bibleData) {
                    // 简化的节号解析
                    const verseMatch = match[0].match(/(\d+)/g);
                    if (verseMatch && verseMatch.length >= 2) {
                        chapterNum = parseInt(verseMatch[0], 10);
                        const verseNum = parseInt(verseMatch[1], 10);
                        
                        const verseKey = `${currentBook} ${chapterNum}:${verseNum}`;
                        const verseText = bibleData[verseKey] || null;
                        
                        foundItems.push({
                            ref: verseKey,
                            text: verseText,
                            book: currentBook,
                            chapter: chapterNum,
                            verse: verseNum
                        });
                    }
                }
            }
            
            // 添加原始行
            htmlParts.push(`<div class="original-line-wrapper">${escapeHtml(line)}</div>`);
            
            // 添加找到的经文
            if (foundItems.length > 0 && withVerses) {
                htmlParts.push(`<div class="processed-references">`);
                foundItems.forEach(item => {
                    htmlParts.push(`<span class="reference-hover">${escapeHtml(item.ref)}`);
                    htmlParts.push(`<span class="verse-preview">`);
                    htmlParts.push(`<div class="verse-preview-header">${escapeHtml(item.ref)}</div>`);
                    
                    if (item.text) {
                        htmlParts.push(`<div class="verse-preview-text">${escapeHtml(item.text)}</div>`);
                    } else {
                        htmlParts.push(`<div class="verse-preview-not-found">经文未在JSON中找到</div>`);
                    }
                    
                    htmlParts.push(`</span></span>`);
                    
                    if (item.text) {
                        htmlParts.push(`<span class="scripture-text">\t${escapeHtml(item.text)}</span>\n`);
                    } else {
                        htmlParts.push(`<span class="scripture-not-found">  -> (经文未在JSON中找到)</span>\n`);
                    }
                });
                htmlParts.push(`</div>`);
            }
            
            htmlParts.push(`</div>`);
            
            // 更新上下文
            if (foundItems.length > 0) {
                const lastItem = foundItems[foundItems.length - 1];
                lastBook = lastItem.book;
                lastChapter = lastItem.chapter;
            }
        }
        
        return {
            chunkIndex,
            html: htmlParts.join(''),
            processed: true,
            error: null,
            stats: {
                linesProcessed: lines.length,
                referencesFound: htmlParts.filter(p => p.includes('reference-hover')).length
            }
        };
        
    } catch (error) {
        return {
            chunkIndex,
            html: `<div class="line-group"><div class="scripture-not-found">处理块 ${chunkIndex + 1} 时出错: ${escapeHtml(error.message)}</div></div>`,
            processed: false,
            error: error.message
        };
    }
}

// Worker消息处理
self.onmessage = function(e) {
    const { type, data } = e.data;
    
    switch (type) {
        case 'init':
            // 初始化Worker数据
            bibleData = data.bibleData;
            bookMap = data.bookMap;
            fullBookNameToAbbrMap = data.fullBookNameToAbbrMap;
            bookChapterLimits = data.bookChapterLimits;
            singleChapterBooks = data.singleChapterBooks;
            
            self.postMessage({
                type: 'init-complete',
                success: true
            });
            break;
            
        case 'process-chunk':
            // 处理文本块
            const result = processTextChunk(
                data.chunk,
                data.chunkIndex,
                data.totalChunks,
                data.options
            );
            
            self.postMessage({
                type: 'chunk-processed',
                data: result
            });
            break;
            
        case 'clear-cache':
            // 清除缓存
            regexCache.clear();
            self.postMessage({
                type: 'cache-cleared',
                success: true
            });
            break;
            
        default:
            self.postMessage({
                type: 'error',
                error: `Unknown message type: ${type}`
            });
    }
};
